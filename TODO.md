faire le ménage dans old variables

organiser proprement le fichier de base styles.scss

faire le trie dans common.scss et garder ce qui est utile 

set les variables du header du nouveau design system dans header.scss

rework le link.scss

ajouter une classe global a chaque page

voir avec jp pour la feuille de style mobile, pour bien les setter, pour le moment c'est setter au jugés

voir aussi si on a une feuille de style special article de blog (titre)






ANGULAR UPDATE SPECIFIC
fix AngularMyDatePickerModule
chartjs
fileupload
