import { Component, OnInit  } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { GridHelperService } from './services/v3/utils/GridHelper.service'; 
import { environment } from '@/../environments/environment';

declare var Cookiebot: any;

@Component({
  selector: 'e-closion',
  templateUrl: './app.component.html'
})

export class AppComponent implements OnInit {
  title = 'app';
  html;

  constructor (
    public translate: TranslateService,
    private router: Router,
    private gridHelper: GridHelperService
  ) {
    this.html = document.querySelector('html');

    const defaultLang = 'fr';
    const urlLang = window.location.pathname.split('/')[1];
    const userLang = ['en', 'fr'].includes(urlLang) ? urlLang : defaultLang;

    translate.setDefaultLang(defaultLang);
    translate.use(userLang);

    router.events.subscribe(event => {
      // route change event
      if (event instanceof NavigationEnd) {
        if (event.url.indexOf('#') === -1) window.scrollTo(0, 0);
        document.querySelector('body').classList.remove('-open-menu');
        this.html.classList.remove('-no-scroll');

        // #############################################
        // ####### A CHANGER AVANT MISE EN LIGNE #######
        // #############################################

        // Google analytics -> force send new page
        // GA 4
        if ((<any>window).gtag) {
          (<any>window).gtag('set', 'page', event.urlAfterRedirects);
          (<any>window).gtag('send', 'pageview');
        }
      }
    });
  }

  ngOnInit() {
		this.cookiesConsentEvents();

    if (!environment.production) {
      this.gridHelper.initialize();
    }
	}
	
	/**
	 * Set up event listeners for Cookiebot's "accept" and "decline" events.
	 * When users accept or decline cookies, reloads the page.
	 */
	cookiesConsentEvents() {
		if (typeof Cookiebot !== 'undefined') {

			window.addEventListener('CookiebotOnAccept', () => {
				if (Cookiebot.changed === true) {
					location.reload();
				}
			}, false);

        window.addEventListener('CookiebotOnDecline', () => {
          if (Cookiebot.changed === true) {
            location.reload();
          }
        }, false);
      }
  	}
}