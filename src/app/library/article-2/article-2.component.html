<div class="grid">
	<a *ngIf="blogPost.coverphoto" class="img-ctn col-t-lg-4 col-12" [routerLink]="['urls.real-estate-blog' | translate, blogPost.slug ]" [attr.rel]="blogPost.no_index === 1 ? 'nofollow noindex' : null">
		<img src="{{ blogPost.coverphoto }}" alt="{{'library.article.alt-image' | translate}}{{ blogPost.title }}">
	</a>
	<div [ngClass]="blogPost.coverphoto ? 'col-t-lg-7' : 'col-t-lg-12'" class="article-info col-12">
		<h3 class="title"><a [routerLink]="['urls.real-estate-blog' | translate, blogPost.slug ]" [attr.rel]="blogPost.no_index === 1 ? 'nofollow noindex' : null">{{ blogPost.title }}</a></h3>
		<p class="date">{{ blogPost.publication_date | localizedDate:'longDate'}}</p>
		<p class="description">{{ blogPost.abstract | shorten: 600: '...' }}</p>
	</div>
</div>
