<div class="grid" [ngClass]="{'-large': isLarge}">
	<a *ngIf="blogPost.coverphoto" class="img-ctn" [ngClass]="isLarge ? 'col-12' : 'col-t-lg-4 col-12'" [routerLink]="['urls.real-estate-blog' | translate, blogPost.slug ]" [attr.rel]="blogPost.no_index === 1 ? 'nofollow noindex' : null">
		<img src="{{ blogPost.coverphoto }}" alt="{{'library.article.alt-image' | translate}}{{ blogPost.title }}">
	</a>
	<div class="article-info" [ngClass]="isLarge ? 'col-12' : (blogPost.coverphoto ? 'col-t-lg-7 col-12' : 'col-t-lg-12 col-12')">
		<h3 class="title"><a [routerLink]="['urls.real-estate-blog' | translate, blogPost.slug ]" [attr.rel]="blogPost.no_index === 1 ? 'nofollow noindex' : null">{{ blogPost.title }}</a></h3>
		<p class="date">{{ blogPost.publication_date | localizedDate:'longDate'}}</p>
	</div>
</div>
