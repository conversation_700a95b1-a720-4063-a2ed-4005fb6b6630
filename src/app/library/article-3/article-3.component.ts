import { Component, OnInit, Input } from '@angular/core';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'lib-article-3',
  templateUrl: './article-3.component.html',
  host: {
    '(click)': 'onClick()'
  }
})

export class Article3Component implements OnInit {
  @Input() blogPost;
  @Input() isLarge: boolean = false;

  constructor(
    private router: Router,
    private translate: TranslateService
  ) { }

  ngOnInit() {
  }

  onClick() {
    const blogUrl = this.translate.instant('urls.real-estate-blog');
    this.router.navigate([blogUrl, this.blogPost.slug]);
  }
}
