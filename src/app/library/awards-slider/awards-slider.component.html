<div id="awards-slider-cpn" class="awards-slider-cpn -invisible -space-medium" *ngIf="awards?.length">
	<div class="container">

		<div class="awards-slider-header">
			<h2 class="-h3">{{ 'library.awards-slider.title' | translate }}</h2>
			<div class="nav-ctn">
				<a class="swiper-button-prev awards-button-prev swiper-btn"></a>
				<a class="swiper-button-next awards-button-next swiper-btn"></a>
			</div>
		</div>

		<div class="swiper-container">
			<div class="swiper-wrapper">
				<div class="swiper-slide" *ngFor="let award of awards;">
					<lib-award-card [award]="award"></lib-award-card>
				</div>
			</div>
		</div>
	</div>
</div>
