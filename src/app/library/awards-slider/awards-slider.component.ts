import { Component, OnInit } from '@angular/core';
import { AwardsService } from '@/services/v3/awards/awards.service';
import Swiper from 'swiper';

@Component({
  selector: 'lib-awards-slider',
  templateUrl: './awards-slider.component.html'
})

export class AwardsSliderComponent implements OnInit {
  awardsSwiper: any;
  awards = [];

  constructor (private awardsService: AwardsService) {
  }

  ngOnInit () { }

  ngAfterViewInit () {
    this.awardsService.getAwards().subscribe(({ success, data }) => {
      this.awards = data;
      if (success) this.initSlider();
    });
  }

  initSlider () {
    const element = document.querySelectorAll('.-invisible')[0];

    setTimeout(() => {
      this.awardsSwiper = new Swiper('.awards-slider-cpn .swiper-container', {
        speed: 1000,
        loop: false,
        observer: true,
        navigation: {
          nextEl: '.awards-button-next',
          prevEl: '.awards-button-prev'
        },
        slidesPerView: 1,
        spaceBetween: 0,
        breakpoints: {
          768: {
            slidesPerView: 3,
            spaceBetween: 40
          },
          1024: {
            slidesPerView: 3,
            spaceBetween: 40
          }
        }
      });

      if (element) element.classList.remove('-invisible');
    }, 1000);
  }
}
