<div class="blog-details-page" *ngIf="post" itemscope itemtype="http://schema.org/Article">
  
	<div class="container -narrow">

		<div class="backTop">
			<a (click)="backTop()" class="icon-arrow_upward"></a>
		</div>

		<div class="-page-head -space-small">
			<h1 itemprop="name" *ngIf="post.title" class="-small">{{post.title}}</h1>

			<div *ngIf="post.abstract">
				<p class="resume" [innerHtml]="post.abstract"></p>
			</div>
			
			<div class="details-info">
				<p class="categories">
					<span *ngIf="post.category">{{post.category_title}}</span>
					<span *ngFor="let tag of post.tags">, {{tag.name}}</span>
				</p>
				<p class="date" itemprop="datePublished" *ngIf="post.publication_date" >{{post.publication_date | localizedDate:'longDate'}}</p>
			</div>
		</div>
	</div>

	<div class="main-img-ctn container -small" *ngIf="post.coverphoto">
		<img itemprop="image" src="{{post.coverphoto}}" alt="{{post.title}}" id="imgTop">
	</div>
  
	<div class="container -narrow">

		<div class="main-text-ctn laraberg" [innerHtml]="contentSanitized"></div>

		<!-- <div *ngIf="iframeUrl" class="iframe-container .col-xs-12" >
			<iframe allowfullscreen allow="fullscreen" style="border:none;width:100%;height:350px;" class="e2e-iframe-trusted-src" [src]="iframeUrl"></iframe>
		</div> -->
			
		<div class="contact-cta no-print">
			<div class="text-ctn">
				<h3 class="title -no-space">{{ 'library.blog-details.contact-cta.title' | translate }}</h3>
				<p itemprop="articleBody" class="description">{{ 'library.blog-details.contact-cta.description' | translate }}</p>
			</div>

			<div class="button-ctn">
				<a [routerLink]="['urls.contact' | translate ]" class="main-button -ghost">{{ 'library.blog-details.contact-cta.button' | translate }}</a>
			</div>
		</div>
	</div>
</div>
