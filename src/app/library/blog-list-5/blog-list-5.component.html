<div class="blog-list-cpn blog-list-5 -space-medium">
	<div class="container">

		<div class="cpn-head">
			<h2 class="">{{ 'library.blog-list-5.title' | translate }}</h2>
			<a [routerLink]="['urls.real-estate-blog' | translate ]" class="small-link right hide-mobile">
        		{{ 'library.blog-list-5.all-posts' | translate }}
				<i class="icon-arrow-right"></i>
			</a>
		</div>

		<div class="article-list-ctn grid -full" id="blog-list">
			<div class="col-12 col-t-lg-6">
				<div itemscope itemtype="http://schema.org/Blog" *ngFor="let blogPost of blogPosts | orderBy: '-publication_date' | filterBy: ['category_slug']: currentCategory | paginate: { id: 'blog-pagination', itemsPerPage: limitPerPage, currentPage: cPage, totalItems:totalLength }; let i = index">
					<lib-article-3 *ngIf="i === 0" itemprop="blogPost" [blogPost]="blogPost" [isLarge]="true"></lib-article-3>
				</div>
			</div>
			<div class="col-12 col-t-lg-6 -right-ctn">
				<div itemscope itemtype="http://schema.org/Blog" *ngFor="let blogPost of blogPosts | orderBy: '-publication_date' | filterBy: ['category_slug']: currentCategory | paginate: { id: 'blog-pagination', itemsPerPage: limitPerPage, currentPage: cPage, totalItems:totalLength }; let i = index">
					<lib-article-3 *ngIf="i > 0 && i < 4" itemprop="blogPost" [blogPost]="blogPost"></lib-article-3>
				</div>
			</div>
		</div>
	</div>
</div>
