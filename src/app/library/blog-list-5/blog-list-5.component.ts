import { Component, OnInit } from '@angular/core';
import { BlogService } from '@/services/v3/blog/blog.service';

@Component({
  selector: 'lib-blog-list-5',
  templateUrl: './blog-list-5.component.html'
})

export class BlogList5Component implements OnInit {
  public blogPosts = [];
  public blogCategories = [];

  public cPage: number = 1;
  public currentCategory: string;
  public totalLength: number;
  public limitPerPage = 9;

  constructor (
    private blog: BlogService
  ) {}

  ngOnInit () {
    // Get posts
    this.blog.getPosts('', '').subscribe(({ data }) => {
      this.blogPosts = data;
      this.totalLength = data.length;
    });

    // Get blog categories
    this.blog.getCategories().subscribe(({ data }) => {
      this.blogCategories = data;
    });
  }

  onPageChange (number: number) {
    this.cPage = number;
    document.getElementById('blog-list').scrollIntoView({ behavior: 'smooth' });
  }
}
