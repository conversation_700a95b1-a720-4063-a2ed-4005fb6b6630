<div class="buy-sheet-page container grid">

	<div class="team-info-wrap col-12 col-t-lg-5">
		<h2 *ngIf="blockTitle" class="page-title">{{ blockTitle }}</h2>
		<p class="page-description" [innerHTML]="'library.buy-sheet.description' | translate"></p>
		<a [routerLink]="['urls.contact' | translate ]" class="main-button -primary">{{ 'library.buy-sheet.button' | translate }}</a>
	</div>

	<div class="page-list-ctn col-12 col-t-lg-7">
		<div class="page-description" [innerHtml]="blockContent"></div>
	</div>

</div>