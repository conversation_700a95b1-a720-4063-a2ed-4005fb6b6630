<div class="custom-content-cpn"  *ngIf="customContent?.left_right_section != null">
	<div class="post-card-ctn"  *ngFor="let Block of customContent.left_right_section">
		<div class="post-card grid" [class.left-post]="Block.orientation == 'left'">
			<div class="img-ctn col-12 col-t-lg-6">
				<div class="filter"></div>
				<img [src]="Block.image" [alt]="Block.title">
			</div>
			<div class="post-card-content col-12 col-t-lg-6">
				<div class="content">
					<h2 class="-small -no-space" *ngIf="Block.title">{{ Block.title }}</h2>
					<div class="description -space-small" *ngIf="Block.text" [innerHTML]="Block.text"></div>
					<a class="main-button -primary"  *ngIf="Block.btn_url" [target]="Block.target" [href]="Block.btn_url">{{ Block.btn_label }}</a>
				</div>
			</div>
		</div>
	</div>
</div>
