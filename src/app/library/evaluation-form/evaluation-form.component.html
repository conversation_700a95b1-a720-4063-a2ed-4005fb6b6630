<div class="evaluation-start-cpn" [class.-hide]="showForm">
    <div class="inner container -narrow">
        <h1 class="-small -no-top-space -center">{{ blockTitle }}</h1>
        <div class="-page-description" [innerHtml]="blockContent"></div>

            <form class="form-row">
                <div class="input-ctn">
                    <input
                    type="search"
                    id="address-input"
                    class="large"
                    placeholder="{{ 'library.evaluation-form.placeholder' | translate }}"
                    />
                </div>
            </form>

            <div class="button-ctn -center">
                <button
                    class="main-button -primary"
                    (click)="onSearchLocation()"
                    [disabled]="!currentLocation"
                >
                    {{ "library.evaluation-form.btn-evaluation" | translate }}
                </button>
            </div>
        </div>  
    </div>

<div class="evaluation-form-cpn" [class.-hide]="!showForm" id="evaluateForm">
    <div class="container -small" [ngClass]="{ send: formSend }">
        <form
            class="alert-form"
            [formGroup]="evaluateForm"
            [ngClass]="{ 'loading-inner': formLoading }"
            (ngSubmit)="onSubmit()"
        >
            <div class="form-head">
                <h1 class="-small -no-space -center">
                    {{ "library.evaluation-form.form.title" | translate }}
                </h1>
            </div>

            <div id="step1" class="step-ctn" [class.show]="currentStep == 1">
                <div class="map-ctn">
                    <div id="street-view">
                        <div id="streetNoResult" class="no-results">
                            <p class="no-results-inner">
                                {{ "library.evaluation-form.form.no-result" | translate }}
                            </p>
                        </div>
                        <div id="streetNoView" class="no-view" *ngIf="!hasStreetView">
                            <p class="">
                                {{ "library.evaluation-form.form.no-view" | translate }}
                            </p>
                        </div>
                    </div>
                    <div class="info-map-ctn" *ngIf="currentLocation">
                        <p class="found -h4">
                            {{ "library.evaluation-form.form.property-found" | translate }}
                        </p>
                        <p class="address --normal">
                            {{ currentLocation.formatted_address }}
                            <span class="-cta-small" (click)="onReturnToSearch()">{{ "library.evaluation-form.form.edit" | translate }}</span>
                        </p>
                    </div>
                </div>

                <div class="too-far-alert" *ngIf="isTooFar">
                    <p class="title">{{ 'library.evaluation-form.form.too-far-alert.title' | translate }}</p>
                    <p class="description">{{ 'library.evaluation-form.form.too-far-alert.description' | translate }}</p>
                </div>

                <div class="form-row">
                    <div class="input-ctn -dual">
                        <label>{{ "library.evaluation-form.form.label.type" | translate }}</label>
                        <div *ngIf="propertyTypes.length > 0">
                            <ng-container *ngIf="translate.currentLang == 'fr'">
                                <ng-select
                                    [searchable]="false"
                                    [items]="propertyTypes"
                                    bindLabel="id"
                                    bindValue="id"
                                    [(ngModel)]="selectedPropertyType"
                                    [ngModelOptions]="{ standalone: true }"
                                    placeholder="{{ 'library.evaluation-form.form.placeholder_choose' | translate }}"
                                    class="rooms-filter align-center"
                                >
                                </ng-select>
                            </ng-container>

                            <ng-container *ngIf="translate.currentLang == 'en'">
                                <ng-select
                                    [searchable]="false"
                                    [items]="propertyTypes"
                                    bindLabel="name"
                                    bindValue="name"
                                    [(ngModel)]="selectedPropertyType"
                                    [ngModelOptions]="{ standalone: true }"
                                    placeholder="{{ 'library.evaluation-form.form.placeholder_choose' | translate }}"
                                    class="rooms-filter align-center"
                                >
                                </ng-select>
                            </ng-container>
                        </div>
                    </div>

                    <div class="input-ctn -dual">
                        <label>{{
                            "library.evaluation-form.form.label.years" | translate
                            }}</label>
                        <input
                            mask="0000"
                            [ngClass]="{ '-error':
                                constructYear.invalid && (constructYear.dirty || constructYear.touched)
                            }"
                            formControlName="constructYear"
                            type="text"
                            name="construction_year"
                        />
                        <div
                            class="form-control-feedback"
                            *ngIf="constructYear.errors && (constructYear.dirty || constructYear.touched)"
                        >
                            <p>{{ "library.evaluation-form.form.errors.constructYear_invalid" | translate }}</p>
                        </div>
                    </div>
                </div>

                <h3 class="-small -line -emphase-title">
                    {{ "library.evaluation-form.form.property_about" | translate }}
                </h3>

                <div class="form-row" formGroupName="name">
                    <div class="input-ctn -dual">
                        <label>{{ "library.evaluation-form.form.label.firstname" | translate }}
                            <span class="required-input">*</span></label>
                        <input
                            type="text"
                            name="first_name"
                            [ngClass]="{ '-error': firstName.invalid && (firstName.dirty || firstName.touched) }"
                            formControlName="firstName"
                        />
                        <div class="form-control-feedback" *ngIf="firstName.errors && (firstName.dirty || firstName.touched)">
                            <p>
                                {{ "library.evaluation-form.form.errors.firstname_required" | translate }}
                            </p>
                        </div>
                    </div>
                    <div class="input-ctn -dual">
                        <label>{{ "library.evaluation-form.form.label.lastname" | translate }}
                            <span class="required-input">*</span>
                        </label>
                        <input type="text"
                            [ngClass]="{ '-error': lastName.invalid && (lastName.dirty || lastName.touched) }"
                            formControlName="lastName"
                            name="last_name"
                        />
                        <div class="form-control-feedback" *ngIf="lastName.errors && (lastName.dirty || lastName.touched)">
                            <p *ngIf="lastName.errors.required">{{ "library.evaluation-form.form.errors.lastname_required" | translate }}</p>
                        </div>
                    </div>
                </div>
                <div class="form-row -bottom" formGroupName="phone">
                    <div class="input-ctn -dual">
                        <label>{{ "library.evaluation-form.form.label.phone" | translate }}
                            <span class="required-input">*</span>
                        </label>
                        <input type="text" 
                            mask="************" 
                            [ngClass]="{ '-error': phoneNumber.invalid && (phoneNumber.dirty || phoneNumber.touched)}"
                            formControlName="phoneNumber"
                            name="phone"
                        />
                    </div>
                    <div class="input-ctn -dual -small">
                        <label></label>
                        <input
                            type="text"
                            name="ext"
                            placeholder="Ext"
                            formControlName="phoneExt"
                        />
                    </div>
                    <div class="form-control-feedback" *ngIf="phoneNumber.errors && (phoneNumber.dirty || phoneNumber.touched)">
                        <p *ngIf="phoneNumber.errors.required">
                            {{
                            "library.evaluation-form.form.errors.phone_required" | translate
                            }}
                        </p>
                        <p *ngIf="phoneNumber.errors.pattern">
                            {{ "library.evaluation-form.form.errors.phone_invalid" | translate }}
                        </p>
                        <p *ngIf="phoneNumber.errors.minlength">
                            {{ "library.evaluation-form.form.errors.phone_invalid" | translate }}
                        </p>
                    </div>
                </div>
                <div class="form-row">
                    <div class="input-ctn">
                        <label>
                            {{ "library.evaluation-form.form.label.email" | translate }}
                            <span class="required-input">*</span>
                        </label>
                        <input
                            type="text"
                            (focus)="isTyping = true"
                            (blur)="isTyping = false"
                            [ngClass]="{ '-error': email.invalid && (email.dirty || email.touched) && !isTyping }"
                            formControlName="email"
                            name="email"
                        />
                        <div class="form-control-feedback" *ngIf=" email.errors && (email.dirty || email.touched) && !isTyping">
                            <p *ngIf="email.errors.required">
                                {{ "library.evaluation-form.form.errors.email_required" | translate }}
                            </p>
                            <p *ngIf="email.errors.pattern">
                                {{ "library.evaluation-form.form.errors.email_invalid" | translate }}
                            </p>
                        </div>
                    </div>
                </div>

                <p class="warning-message --small">
                    {{ "library.evaluation-form.form.submit-validation" | translate }}
                </p>

                <p class="-page-required">{{ "library.evaluation-form.form.required" | translate }}</p>

                <div class="button-ctn">
                    <button class="main-button -primary" [disabled]="!evaluateForm.valid">
                        {{ "library.evaluation-form.form.submit" | translate }}
                    </button>
                </div>
            </div>
        </form>
        <div *ngIf="formLoading" class="form-loader">
            <div class="loading-circle"></div>
        </div>
        <div class="form-response" [ngClass]="{ show: formSend }">
            <h1 class="-small">
                {{ "library.evaluation-form.form.title" | translate }}
            </h1>
            <ng-container *ngIf="successMessage">
                <p class="message">
                    {{ "library.evaluation-form.form.submit-message" | translate }}
                </p>
                <div class="button-ctn">
                    <a class="main-button -primary" (click)="resetForm()">{{
                        "library.evaluation-form.form.submit-another" | translate
                        }}</a>
                </div>
            </ng-container>
            <ng-container *ngIf="errorMessage">
                <p class="message">
                    {{ "library.evaluation-form.form.errors.submit-message" | translate }}
                </p>
                <div class="button-ctn">
                    <a class="main-button -primary" (click)="retry()">{{
                        "library.evaluation-form.form.errors.back" | translate
                        }}</a>
                </div>
            </ng-container>
        </div>
    </div>
</div>
