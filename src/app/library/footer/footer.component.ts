import { Component, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { Router } from '@angular/router';

@Component({
  selector: 'lib-footer',
  templateUrl: './footer.component.html'
})

export class FooterComponent implements OnInit {
  urlTranslated: string;
  year = (new Date()).getFullYear();
  showPopup = false;
  isClosing = false;

  constructor (
    public translate: TranslateService,
    private router: Router
  ) {}

  ngOnInit() {
    // Check if popup has been shown before
    const popupShown = localStorage.getItem('popupShown');
    
    if (!popupShown) {
      // Get the evaluation URL from translations
      this.translate.get('urls.real-estate-online-evaluation').subscribe(url => {
        // Only show popup if we're not already on the evaluation page (matching the url)
        if (this.router.url !== '/' + url && !this.router.url.includes(url)) {
          setTimeout(() => {
            this.showPopup = true;
          }, 1000);
        }
      });
    }
  }

  closePopup(): void {
    this.isClosing = true;
    setTimeout(() => {
      this.showPopup = false;
      this.isClosing = false;
      localStorage.setItem('popupShown', 'true');
    }, 500); // Durée de l'animation
  }

  onPopupButtonClick(): void {
    localStorage.setItem('popupShown', 'true');
  }
}
