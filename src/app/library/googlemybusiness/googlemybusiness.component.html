<div class="reviews-slider-cpn" *ngIf="reviews?.reviews?.length">
    <div class="container grid">
        <div class="col-12 col-t-lg-5 text"> 
            <p class="undertitle">{{ "library.reviews.undertitle" | translate }}</p>
            <h2 class="title">{{ "library.reviews.title" | translate }}</h2>
        </div>

        <div class="swiper-container reviews-list-ctn col-12 col-t-lg-7">
            <div class="swiper-wrapper reviews-list-inner">

                <div class="swiper-slide review" *ngFor="let review of reviews.reviews">
                    <div class="review-body">
                        <div class="review-text">{{ filterText(review.comment) }}</div>
                    </div>
                    <div class="review-header">
                        <div class="review-header-left">
                            <img class="reviewer-image" src="{{ review.reviewer.profilePhotoUrl }}" alt="Reviewer Image">
                        </div>
                        <div class="review-header-right">
                            <div class="reviewer-name">{{ review.reviewer.displayName }}</div>
                            <div class="reviewer-rating">
                                <div class="rating-stars">
                                    <i *ngFor="let star of [1, 2, 3, 4, 5]" class="icon-star" [ngClass]="{'checked': star <= getNumericRating(review.starRating)}"></i>
                                </div>
                                <div class="rating-value">{{ review.rating }}</div>
                            </div>
                            <!-- <div class="reviewer-date">{{ formatDate(review.createTime) }}</div> -->
                        </div>
                    </div>
                </div>
            </div>

            
            <!-- <div class="swiper-pagination"></div> -->
        </div>
    </div>

    <div class="pagination container">
        <div class="swiper-button-prev swiper-btn"><i></i></div>
        <div class="swiper-button-next swiper-btn"><i></i></div>
    </div>
</div>