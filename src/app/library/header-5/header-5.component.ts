import { Component, OnInit, Input } from '@angular/core';
import { LocalStorageService } from 'ngx-webstorage';
import { LangChangeEvent, TranslateService } from '@ngx-translate/core';
import { Router } from '@angular/router';

import { FavoritesService } from '@/services/v3/favorites/favorites.service';
import { BlogService } from '@/services/v3/blog/blog.service';

@Component({
  selector: 'lib-header-5',
  templateUrl: './header-5.component.html'
})

export class Header5Component implements OnInit {
  @Input() transparent: string;
  @Input() specialColor: string;
  @Input() logoSwap: boolean;
  public post: any = {};

  urlTranslated: string;

  constructor (private blog: BlogService,
    private storage: LocalStorageService,
    private favoritesService: FavoritesService,
    public translate: TranslateService,
    private router: Router
  ) {
    translate.onLangChange.subscribe((event: LangChangeEvent) => {
      if (this.urlTranslated) {
        this.router.navigateByUrl(this.urlTranslated);
      }
    });
  }

  ngOnInit () {
  }

  ngAfterViewInit () {
    const favoriteList = this.storage.retrieve('propertiesFavorite');

    if (favoriteList) {
      const numberOfFavorite = favoriteList.length;
      const favoriteNumberElement = document.getElementById('favorite-number');
      favoriteNumberElement.textContent = String(numberOfFavorite);

      this.favoritesService.getMessage().subscribe(message => {
        const favoriteList = this.storage.retrieve('propertiesFavorite');
        const numberOfFavorite = favoriteList.length;
        favoriteNumberElement.textContent = String(numberOfFavorite);
      });
    }
  }

  onOpenPanel ($event) {
    $event.preventDefault();
    document.body.classList.toggle('-open-menu');
    document.documentElement.classList.toggle('-no-scroll');
  }

  switchLang (lang: string) {
    let url = this.router.url;
    let call = true;
    let param;
    const _temp = url.split('/');

    if (url.indexOf('/propriete') !== -1 || url.indexOf('/property/') !== -1) {
      url = '/' + _temp[1] + '/' + _temp[2];
      param = _temp[3] + '/' + _temp[4] + '/' + _temp[5];
      if (_temp[6]) param += '/' + _temp[6];
    } else if (url.indexOf('/groupes-de-proprietes') !== -1 || url.indexOf('/property-groups/') !== -1) {
      url = '/' + _temp[1] + '/' + _temp[2];
      param = _temp[3];
    } else if (url.indexOf('/blogue-immobilier') !== -1 || url.indexOf('/blog-real-estate-news') !== -1) {
      url = '/' + _temp[1] + '/' + _temp[2];
      if (!_temp[3]) param = _temp[3];
      else {
        call = false;
        this.blog.getPost(_temp[3]).subscribe(data => {
          this.post = data.data;
          param = this.post['slug_' + (_temp[1] === 'en' ? 'en' : 'fr')]; // This can probably be optimized
          this.urlTranslated = this.translate.instant('urls.translate.' + url);

          if (param) this.urlTranslated += ('/' + param);
          this.translate.use(lang);
        });
      }
    } else if (url.indexOf('/secteurs') !== -1 || url.indexOf('/neighborhoods') !== -1) {
      url = '/' + _temp[1] + '/' + _temp[2];
      param = _temp[3];
    } else if (url.indexOf('/investissement') !== -1 || url.indexOf('/real-estate-projects') !== -1) {
      url = '/' + _temp[1] + '/' + _temp[2];
      param = _temp[3];
    }

    if (call) {
      this.urlTranslated = this.translate.instant('urls.translate.' + url);
      if (param) this.urlTranslated += '/' + param;
      this.translate.use(lang);
    }
  }
}
