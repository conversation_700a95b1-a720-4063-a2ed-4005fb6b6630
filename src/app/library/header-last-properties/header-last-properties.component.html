<div *ngIf="properties?.length" class="header-last-properties">
	<p class="title">{{ "library.header.last-inscriptions" | translate }}</p>
	<ul class="list">
		<li *ngFor="let property of properties">
			<div class="image-ctn">
				<img
					[src]="property.ext_coverphoto"
					alt="{{ property.ext_address }}"
				/>
			</div>
			<a
				[routerLink]="['urls.property-single' | translate] + property.url"
				title="{{ property.ext_address }}, {{ property.municipality_label }}"
			>
				{{ property.property_type }}
				<small>
					{{ property.ext_address }}<br />
					{{ property.municipality_label }}
				</small>
			</a>
		</li>
	</ul>
</div>