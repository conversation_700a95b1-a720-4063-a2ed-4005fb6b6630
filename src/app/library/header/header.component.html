<lib-header-panel></lib-header-panel>

<header id="header" [ngClass]="{'transparent': transparent === 'true', 'special': specialColor === 'true'}">
	<div class="main-header">
		<div class="primary-header">
			<div class="logo-ctn -white" itemscope itemtype="http://schema.org/Organization">
				<a itemprop="url" [routerLink]="['urls.home' | translate]"><img src="assets/images/turmel/logo/main-white.svg" alt="{{ 'client.name' | translate }} {{ 'library.header.alt-image' | translate }}"></a>			
			</div>
			<div class="logo-ctn -black" itemscope itemtype="http://schema.org/Organization">
				<a itemprop="url" [routerLink]="['urls.home' | translate]"><img src="assets/images/turmel/logo/main-black.svg" alt="{{ 'client.name' | translate }} {{ 'library.header.alt-image' | translate }}"></a>			
			</div>
			<div class="main-menu-ctn">
				<nav class="main-menu">
					<li class="item-menu" routerLinkActive="active-child" [routerLinkActiveOptions]="{exact: true}">
						<a>{{ "library.header.team" | translate }} <i class="icon-dropdown-menu"></i></a>
						<ul class="secondary-ul">
							<li>
							<a [routerLink]="['urls.real-estate-agents-3' | translate]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ "library.header.our-team-3" | translate }}</a>
							</li>
							<li>
								<a [routerLink]="['urls.specialists' | translate]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ "library.header.specialists" | translate }}</a>
							</li>
						</ul>
					</li>
				<!--  <li class="item-menu">
						<a href="">{{ "library.header.neighborhoods" | translate }}</a>
					</li> -->
					<li class="item-menu" routerLinkActive="active-child" [routerLinkActiveOptions]="{exact: true}">
						<a>{{ "library.header.properties" | translate }}  <i class="icon-dropdown-menu"></i></a>
						<div class="secondary-ul multi-column">
							<ul>
								<li class="item-link">
									<a [routerLink]="['urls.search-properties' | translate ]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ "library.header.search-properties" | translate }}</a>
								</li>
								<li class="item-link">
									<a [routerLink]="['urls.property-group' | translate ]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ "library.header.property-groups" | translate }}</a>
								</li>
								<li class="item-link">
									<a [routerLink]="['urls.neighborhoods' | translate ]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ "library.header.neighborhoods" | translate }}</a>
								</li>
							</ul>
							<lib-header-last-properties></lib-header-last-properties>
						</div>
					</li>
					<li class="item-menu" routerLinkActive="active-child" [routerLinkActiveOptions]="{exact: true}">
						<a>{{ "library.header.buy" | translate }}  <i class="icon-dropdown-menu"></i></a>
						<ul class="secondary-ul">
							<li class="item-link">
								<a [routerLink]="['urls.real-estate-alert' | translate ]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ "library.header.alert" | translate }}</a>
							</li>
							<li class="item-link">
								<a [routerLink]="['urls.buy-house' | translate ]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ "library.header.buying-tips" | translate }}</a>
							</li>
						</ul>
					</li>
					<li class="item-menu" routerLinkActive="active-child" [routerLinkActiveOptions]="{exact: true}">
						<a>{{ "library.header.sell" | translate }}  <i class="icon-dropdown-menu"></i></a>
						<ul class="secondary-ul">
							<li class="item-link">
								<a [routerLink]="['urls.real-estate-online-evaluation' | translate ]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ "library.header.evaluate-online" | translate }}</a>
							</li>
							<li class="item-link">
								<a [routerLink]="['urls.sell-house' | translate ]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ "library.header.selling-tips" | translate }}</a>
							</li>
						</ul>
					</li>
					<!-- <li class="item-menu">
						<a [routerLink]="['urls.real-estate-blog' | translate ]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ "library.header.blog" | translate }}</a>
					</li> -->

					<li class="item-menu">
						<a [routerLink]="['urls.contact' | translate ]" [routerLinkActive]="'active'" [routerLinkActiveOptions]="{exact:true}">{{ "library.header.contact" | translate }}</a>
					</li>
				</nav>
			</div>
		</div>
		<div class="secondary-header">
			<a class="switch" (click)="switchLang(translate.currentLang == 'fr' ? 'en' : 'fr')">{{ "global.switchlang" | translate }}</a>
			<a itemprop="telephone" href="tel:{{ 'client.phone' | translate }}"><i class="icon-phone"></i>{{ 'client.phone' | translate | phone }}</a>
			<img class="special-logo -black" src="assets/images/turmel/logo/rl-noir.png" alt="">
			<img class="special-logo -white" src="assets/images/turmel/logo/rl-blanc.png" alt="">
		</div>

		<a href="#" class="header-menu-toggle" (click)="onOpenPanel($event)"><span></span> <span></span> <span></span></a>

	</div>
</header>
