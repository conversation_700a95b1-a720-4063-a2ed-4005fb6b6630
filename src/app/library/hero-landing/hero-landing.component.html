<div class="hero-landing-cpn -desktop {{ suppClass }}">

    <img class="background -desktop" src="{{ heroImage }}" alt="{{ heroTitle }}">
    <img class="background -mobile" src="{{ heroImageMobile }}" alt="{{ heroTitle }}">

    <div class="container">
        <div class="text-ctn">
            <h1 class="title -small">{{ heroTitle }}</h1>
            <div class="button-ctn" *ngIf="heroButton1 && heroButton2 && heroButton3">
                <a class="main-button -white -cta-medium" [routerLink]="['urls.sell-house' | translate]">{{ heroButton1 }}</a>
                <a class="main-button -white -cta-medium" [routerLink]="['urls.buy-house' | translate]">{{ heroButton2 }}</a>
                <a class="main-button -white -cta-medium" [routerLink]="['urls.search-properties' | translate]">{{ heroButton3 }}</a>
            </div>
        </div>
    </div>
    
</div>

<div class="hero-landing-cpn -mobile {{ suppClass }}">
    <div class="container">
        <div class="text-ctn">
            <h1 class="title -small">{{ heroTitle }}</h1>
            <div class="button-ctn" *ngIf="heroButton1 && heroButton2 && heroButton3">
                <a class="main-button -white -cta-medium" [routerLink]="['urls.sell-house' | translate]">{{ heroButton1 }}</a>
                <a class="main-button -white -cta-medium" [routerLink]="['urls.buy-house' | translate]">{{ heroButton2 }}</a>
                <a class="main-button -white -cta-medium" [routerLink]="['urls.search-properties' | translate]">{{ heroButton3 }}</a>
            </div>
        </div>
    </div>    
</div>