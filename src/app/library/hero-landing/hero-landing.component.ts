import { Component, OnInit, Input } from '@angular/core';

@Component({
  selector: 'lib-hero-landing',
  templateUrl: './hero-landing.component.html'
})

export class HeroLandingComponent implements OnInit {
  @Input() heroImage;
  @Input() heroImageMobile;
  @Input() heroTitle;
  @Input() heroButton1;
  @Input() heroButton2;
  @Input() heroButton3;
  @Input() suppClass;

  constructor () { }

  ngOnInit () {
  }
}
