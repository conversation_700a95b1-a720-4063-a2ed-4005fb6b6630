<div class="homestaging-slider-cpn">
    <div class="container">
        <div class="grid" *ngIf="homestagingsItems?.length > 0">
            <div class="text-ctn col-12 col-t-lg-5">
                <span class="subtitle -eyebrow">{{ 'library.homestaging-slider.subtitle' | translate }}</span>
                <h2 class="title">{{blockTitle}}</h2>
                <div class="description page-description" [innerHTML]="blockContent"></div>
            </div>
            <div class="slider-ctn col-12 col-t-lg-7">
                <lib-slider-default [slides]="homestagingsItems" [showDesc]="true"></lib-slider-default>
            </div>
        </div>
 
        <div class="team-info-box -large">
            <div class="team-info-box-content">
                <div>
                    <h3 class="title">{{ 'library.homestaging-slider.infobox.title' | translate }}</h3>
                    <p class="description">{{ 'library.homestaging-slider.infobox.description' | translate }}</p>
                </div>
                <div class="col-sm-4 col-md-push-1 col-md-3">
                    <a [routerLink]="['urls.contact' | translate ]" class="main-button -primary">{{ 'library.homestaging-slider.infobox.button' | translate }}</a>
                </div>
            </div>
        </div>
    </div>
</div>