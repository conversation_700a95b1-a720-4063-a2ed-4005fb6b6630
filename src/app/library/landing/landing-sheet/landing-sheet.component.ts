import { Component, OnInit, Input } from '@angular/core';

@Component({
  selector: 'lib-landing-sheet',
  templateUrl: './landing-sheet.component.html'
})

export class LandingSheetComponent implements OnInit {
  @Input() campaign;

  constructor () {
  }

  ngOnInit () {
    if (this.campaign && this.campaign.cta_text) {
      this.campaign.cta_text = this.stripTags(this.campaign.cta_text);
    }
  }

  stripTags(input: string): string {
    return input.replace(/<\/?[^>]+(>|$)/g, "");
  }

  sendAnalytics() {
    // Google analytics -> force send new page
    // GA 3
    (<any>window).ga('send', 'event', 'cta-click', 'cta-' + this.campaign.slug);
    // GA 4
    (<any>window).gtag('send', 'event', 'cta-click', 'cta-' + this.campaign.slug);
  }
}
