<div class="neighborhood-avgcost-cpn -space-medium" *ngIf="neighborhood?.averageCostData">
  <div class="container -small">
    <h2 class="-center" *ngIf="neighborhood.averageCostData">
      {{ 'library.neighborhood-avgcost.title' | translate }}
    </h2>

    <div class="chart-ctn">
      <ng-container>
        <canvas baseChart
            [type]="'bar'"
            [datasets]="neighborhood.averageCostData"
            [labels]="neighborhood.averageCostLabels"
            [options]="lineChartOptions"
            [legend]="true"
            (chartHover)="chartHovered($event)">
          </canvas>
      </ng-container>
    </div>

  </div>
</div>
