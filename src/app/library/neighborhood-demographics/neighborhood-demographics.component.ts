import { Component, OnInit, Input, SimpleChanges } from '@angular/core';
import { ChartData, ChartOptions } from 'chart.js';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'lib-neighborhood-demographics',
  templateUrl: './neighborhood-demographics.component.html'
})

export class NeighborhoodDemographicsComponent implements OnInit {
  @Input() neighborhood;
  @Input() formatTitle: Function;

  ageData: ChartData<'doughnut'> = { datasets: [] };
  householdData: ChartData<'doughnut'> = { datasets: [] };
  languageData: ChartData<'doughnut'> = { datasets: [] };

  chartOptions: ChartOptions = {};
  colors = ['#151515', '#F1002C', '#B4A669', '#999', '#797565'];

  constructor (
    private translate: TranslateService
  ) {}

  ngOnInit (): void {

  }

  ngOnChanges (changes: SimpleChanges): void {
    if (changes.neighborhood && this.neighborhood) {
      this.initCharts();
    }
  }

  chartHovered (event: any): void {
    // console.log(event);
  }

  initCharts () {
    this.ageData = {
      labels: this.getAgeLabels(),
      datasets: [{
        data: [
          this.neighborhood.perc_14,
          this.neighborhood.perc_15_24,
          this.neighborhood.perc_25_44,
          this.neighborhood.perc_45_64,
          this.neighborhood.perc_65
        ],
        backgroundColor: this.colors,
        borderColor: '#fff',
        borderWidth: 2
      }]
    };

    this.householdData = {
      labels: this.getHouseholdLabels(),
      datasets: [{
        data: [
          this.neighborhood.perc_household_1,
          this.neighborhood.perc_household_2,
          this.neighborhood.perc_household_3,
          this.neighborhood.perc_household_4,
          this.neighborhood.perc_household_5
        ],
        backgroundColor: this.colors,
        borderColor: '#fff',
        borderWidth: 2
      }]
    };

    this.languageData = {
      labels: this.getLanguageLabels(),
      datasets: [{
        data: [
          this.neighborhood.perc_french,
          this.neighborhood.perc_english,
          this.neighborhood.perc_other
        ],
        backgroundColor: this.colors,
        borderColor: '#fff',
        borderWidth: 2
      }]
    };

    // Chart options
    this.chartOptions = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        tooltip: {
          enabled: true
        },
        legend: {
          position: 'top',
          labels: {
            usePointStyle: true
          }
        }
      }
    };
  }

  getAgeLabels () {
    return [
      this.translate.instant('library.neighborhood-demographics.age.label14less'),
      this.translate.instant('library.neighborhood-demographics.age.label1524'),
      this.translate.instant('library.neighborhood-demographics.age.label2544'),
      this.translate.instant('library.neighborhood-demographics.age.label4564'),
      this.translate.instant('library.neighborhood-demographics.age.label65more')
    ];
  }

  getHouseholdLabels () {
    return [
      this.translate.instant('library.neighborhood-demographics.groups.label1'),
      this.translate.instant('library.neighborhood-demographics.groups.label2'),
      this.translate.instant('library.neighborhood-demographics.groups.label3'),
      this.translate.instant('library.neighborhood-demographics.groups.label4'),
      this.translate.instant('library.neighborhood-demographics.groups.label5')
    ];
  }

  getLanguageLabels () {
    return [
      this.translate.instant('library.neighborhood-demographics.lang.labelfrench'),
      this.translate.instant('library.neighborhood-demographics.lang.labelenglish'),
      this.translate.instant('library.neighborhood-demographics.lang.labelother')
    ];
  }
}
