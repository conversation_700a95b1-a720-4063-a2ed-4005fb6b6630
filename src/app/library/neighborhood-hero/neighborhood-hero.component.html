<div class="neighborhood-hero-cpn" itemscope itemtype="http://schema.org/Place">

    <img class="background" itemprop="photo" src="{{ neighborhood.header_image }}" alt="{{ neighborhood.name }}">

    <div class="container">
        <div class="text-ctn">
            <h1 class="title -small">{{ neighborhood.name }}</h1>
        </div>
    </div> 
</div>

<ng-container *ngIf="neighborhood">
	<div class="full-screen-cpn" id="fullSlider" *ngIf="neighborhood.video_url">

		<div class="close" (click)="onCloseFullScreen()">
			<span class="icon-close"></span>
		</div>

		<div class="background" (click)="onCloseFullScreen()"></div>
    
		<div class="video-ctn container">
			<div class="video-inner">
				<p class="e2e-iframe-trusted-src">{{ neighborhood.video_url.title }}</p>
				<iframe id="video-yt" class="e2e-iframe-trusted-src" [src]="videoUrl" frameborder="0" allow="autoplay; encrypted-media" allowfullscreen></iframe>
			</div>
		</div>

	</div>
</ng-container>
