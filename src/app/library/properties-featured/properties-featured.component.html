<div class="properties-featured-cpn" *ngIf="properties">
  <div class="container grid">

    <div class="col-12 col-t-lg-4">
      <span class="subtitle -eyebrow">{{ 'library.properties-featured.subtitle' | translate }}</span>
      <h2 class="title">
        {{ 'library.properties-featured.title' | translate }}
      </h2>
      <div class="description">
        <p>{{ 'library.properties-featured.text' | translate }}</p>
        <!-- <a class="small-link right" [routerLink]="['urls.search-properties' | translate ]">
          {{ 'library.properties-featured.btn' | translate }}<i class="icon-arrow-right"></i>
        </a> -->
      </div>
    </div>

    <div class="col-12 col-t-lg-8 slider">
      <div class="properties-featured-swiper swiper-container" #swiperContainer>
        <div class="swiper-wrapper">
          <div class="swiper-slide" *ngFor="let property of properties">
            <lib-property-featured [property]="property"></lib-property-featured>
          </div>
        </div>

        <div class="swiper-button-prev swiper-btn" #prevButton [ngClass]="{hidden: properties.length == 1}"><i></i></div>
        <div class="swiper-button-next swiper-btn" #nextButton [ngClass]="{hidden: properties.length == 1}"><i></i></div>
      </div>
    </div>

  </div>
</div>
