<div *ngIf="properties" class="properties-list-cpn -space-medium">
	<div class="container">
		<div class="cpn-head">
			<div class="title-ctn">
				<img src="assets/images/SVG/icons/ping-header.svg" alt="">
				<h2 class="title">{{ "library.properties-list-2.title" | translate }}</h2>
			</div>
			<a [routerLink]="['urls.search-properties' | translate ]" class="small-link right hide-mobile">{{ "library.properties-list-2.all" | translate }}<i class="icon-arrow-right"></i></a>
		</div>
		<div class="properties-list-ctn">
			<div class="properties" *ngFor="let property of properties">
				<lib-properties [property]="property"></lib-properties>
			</div>
		</div>

		<a [routerLink]="['urls.search-properties' | translate ]" class="small-link right show-mobile">{{ "library.properties-list-2.all" | translate }}<i class="icon-arrow-right"></i></a>
	</div>
</div>
