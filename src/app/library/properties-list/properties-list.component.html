<div *ngIf="properties" class="properties-list-cpn">
	<div class="container">
		<div class="cpn-head">
			<h2 class="-h3">{{ "library.properties-list.title" | translate }}</h2>
		</div>
		<div class="properties-list-ctn">
			<div class="properties" *ngFor="let property of properties">
				<lib-properties [property]="property"></lib-properties>
			</div>
		</div>

		<a [routerLink]="['urls.search-properties' | translate ]" class="main-button -primary">{{ "library.properties-list.all" | translate }}</a>
	</div>
</div>
