<div *ngIf="broker" class="team-card-wrap team-card-cpn">
	<div class="team-card-1" itemscope itemtype="http://schema.org/Person">
		<div class="card-content">
			<div class="img-ctn" *ngIf="broker.url_photo">
				<img itemprop="image" src="{{ broker.url_photo }}" alt="{{broker.firstname}} {{broker.lastname}}">
			</div>
			<div class="info-ctn">
				<p itemprop="name" class="-h4">{{broker.firstname}} {{broker.lastname}}</p>
				<p itemprop="jobTitle" class="role">{{broker.professionnal_title}}</p>
				<a itemprop="telephone" href="tel:{{broker.phonenumber_1}}" class="number"><i class="icon-mobile"></i>{{broker.phonenumber_1 | phone}}</a>
				<a *ngIf="broker.phonenumber_2" itemprop="telephone"  href="tel:{{broker.phonenumber_2}}" class="number"><i class="icon-mobile"></i>{{broker.phonenumber_2 | phone}}</a>
			</div>
		</div>
		<a class="main-button -primary" (click)="onOpenFullScreen()">{{ "library.property-broker-card-1.contact" | translate }}</a>
	</div>
</div>