import { Component, Input, OnInit } from '@angular/core';
import { UntypedFormBuilder, UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';

import { ContactService } from '@/services/v3/contact/contact.service';
import { InscriptionsService } from '@/services/v3/inscriptions/inscriptions.service';

import { Subscription } from 'rxjs';
import { ReCaptchaV3Service } from 'ng-recaptcha';
import { CalendarValue, DpDatePickerModule, IDate } from 'ng2-date-picker';

@Component({
  selector: 'lib-property-form-contact',
  templateUrl: './property-form-contact.component.html'
})

export class PropertyFormContactComponent implements OnInit {
  @Input() mls;

  timeRange: any[];

  formSend: boolean = false;
  formLoading: boolean = false;
  successMessage: boolean = false;
  errorMessage: boolean = false;
  isTyping = false;
  hoursIsShow: boolean = false;

  contactForm: UntypedFormGroup;
  firstName: UntypedFormControl;
  lastName: UntypedFormControl;
  phone: UntypedFormControl;
  email: UntypedFormControl;
  message: UntypedFormControl;
  selectedDate = new Date();
  selectedTime: any;
  subject: any;

  datePickerConfig = {
      min: new Date().toLocaleDateString(this.translate.currentLang === 'fr' ? 'fr-FR' : 'en-US', {
      day: '2-digit',
      month: '2-digit', 
      year: 'numeric'
    }).split('/').join('/'),
    format: this.translate.currentLang === 'fr' ? 'DD/MM/YYYY' : 'MM/DD/YYYY',
    firstDayOfWeek: this.translate.currentLang === 'fr' ? 'mo' : 'su'
  };

  locale = 'fr';

  // Recaptcha v3 Subscription
  private submitExecutionSubscription: Subscription

  constructor (
    private recaptchaV3Service: ReCaptchaV3Service,
    private formBuilder: UntypedFormBuilder,
    public translate: TranslateService,
    private contactService: ContactService,
    private inscriptionsService: InscriptionsService
  ) {
    this.translate.get('library.property-form-contact.hours').subscribe(res => {
      this.timeRange = res;
    });
  }

  ngOnInit () {
    this.createFormControls();
    this.createForm();
    this.delayRCDisplay();
  }

  delayRCDisplay () {
    setTimeout(() => {
      const result = document.getElementsByClassName('grecaptcha-badge');
      const recaptcha = result.item(0) as HTMLElement;
      recaptcha.style.visibility = 'visible';
    }, 2000);
  }

  ngOnDestroy () {
    const result = document.getElementsByClassName('grecaptcha-badge');
    const recaptcha = result.item(0) as HTMLElement;
    recaptcha.style.visibility = 'hidden';

    if (this.submitExecutionSubscription) {
      this.submitExecutionSubscription.unsubscribe();
    }
  }

  private createFormControls () {
    this.firstName = new UntypedFormControl('', Validators.required);
    this.lastName = new UntypedFormControl('', Validators.required);
    this.phone = new UntypedFormControl('', [
      Validators.required,
      Validators.pattern('[0-9]+'),
      Validators.minLength(10)
    ]);
    this.email = new UntypedFormControl('', [
      Validators.required,
      Validators.pattern('^\\w+([\\.-]?\\w+)*@\\w+([\\.-]?\\w+)*(\\.\\w{2,3})+$')
    ]);
    this.message = new UntypedFormControl('', Validators.required);
  }

  private createForm () {
    this.translate.get('library.property-form-contact.subject').subscribe(res => {
      this.subject = res;
    });

    this.contactForm = this.formBuilder.group({
      subject: this.subject,
      firstName: this.firstName,
      lastName: this.lastName,
      phone: this.phone,
      email: this.email,
      code: '',
      burCode: '',
      message: this.message,
      datetime: '',
      mls: ''
    });
  }

  changeDate(date: IDate) {
    this.hoursIsShow = true;
  }


  onCloseFullScreen () {
    document.querySelectorAll('#contact-pop-up')[0].classList.remove('-opened');
    document.documentElement.classList.toggle('-no-scroll');
  }

  onSubmit () {
    if (!this.contactForm.valid) return false;
    this.formLoading = true;

    this.submitExecutionSubscription = this.recaptchaV3Service.execute('submit').subscribe(token => {
      if (this.selectedDate) {
        const formattedDate = this.selectedDate.toLocaleDateString('en-US', {
          day: 'numeric',
          month: 'numeric',
          year: 'numeric'
        });
        this.contactForm.value.datetime = formattedDate;
        if (this.selectedTime) this.contactForm.value.datetime += ' ' + this.selectedTime;
      }

      this.contactForm.value.subject = this.subject;
      this.contactForm.value.mls = this.mls;
      this.contactForm.value.code = document.getElementById('dynamic-broker-name').getAttribute('data-code');
      this.contactForm.value.burCode = document.getElementById('dynamic-broker-name').getAttribute('data-burcode');
      this.contactForm.value.token_captcha = token;

      this.contactService.postContactProperty(this.contactForm.value).subscribe(response => {
        this.formSend = true;
        this.formLoading = false;

        if (response.success) this.successMessage = true;
        else this.errorMessage = true;
      });
    },
    error => console.error(error));
  }

  resetForm () {
    this.onCloseFullScreen();
    this.contactForm.reset();
    this.selectedDate = new Date();
    this.selectedTime = '';
    this.hoursIsShow = false;
    setTimeout(() => { this.formSend = false; }, 400);
  }

  retry () {
    this.formSend = true;
  }
}
