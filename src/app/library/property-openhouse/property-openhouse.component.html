<div class="property-visit-cpn" *ngIf='openHouses?.length > 0'>
	<h3 class="-emphase-title">{{ 'library.property-openhouse.title' | translate }}</h3>

	<div class="visit-ctn table-ctn -center">
		<div class="visit table-row -flex-between" *ngFor="let openhouseDate of openHouses;">
    	<p class="date"><i class="icon-calendar"></i> <span>{{ openhouseDate.start_date | localizedDate:'longDate' }}</span></p>
		<p class="hour">{{ 'library.property-openhouse.from' | translate }} {{ openhouseDate.start_time}} {{ 'library.property-openhouse.to' | translate }} {{ openhouseDate.end_time}}</p>
		<a href="https://www.addevent.com/dir/?client=admkuFarczmhIRGvMmkd41981&start={{ openhouseDate.start_date | date:'short':'':'en' }}&end={{ openhouseDate.end_date | date:'short':'':'en' }}&title={{ 'library.property-openhouse.openhouse-at' | translate }} {{ address }}" class="main-button -secondary-small" target="_blank">{{ 'library.property-openhouse.add-to-calendar' | translate }}</a>
		</div>
	</div>
</div>
