<div class="property-sheet container grid" *ngIf="property">
	<div class="property-sheet-content col-12 col-t-lg-8">
		<lib-property-description [property]='property'></lib-property-description>
		<div *ngIf="detectMobile" class="property-mobile-sidebar">
			<div *ngFor="let broker of brokers;">
				<lib-property-broker-card-1 [broker]='broker'></lib-property-broker-card-1>
			</div>
			<lib-property-share [shareUrl]='shareUrl'></lib-property-share>
			<lib-property-favorite [mls]='property.mls'></lib-property-favorite>
		</div>
		<lib-property-openhouse [openHouses]="openHouses" [address]='property.ext_address' [mls]='property.mls'></lib-property-openhouse>
		<lib-property-details [property]='property'></lib-property-details>
		<lib-property-rooms [rooms]='rooms'></lib-property-rooms>
		<lib-property-characteristics [characteristics]='characteristics'></lib-property-characteristics>
		<lib-property-addenda [addenda]='addenda'></lib-property-addenda>
		<lib-property-inclusion [included]='property.included' [excluded]='property.excluded'></lib-property-inclusion>
		<lib-property-downloads [documentsList]='documentsList'></lib-property-downloads>
	</div>
	<div class="col-12 col-t-lg-4">
		<div *ngIf="!detectMobile" class="property-sheet-sidebar">
			<div *ngFor="let broker of brokers;">
				<lib-property-broker-card-1 [broker]='broker'></lib-property-broker-card-1>
			</div>
			<lib-property-share></lib-property-share>
		</div>
		<lib-property-form-contact [mls]="property.mls"></lib-property-form-contact>
	</div>
</div>
