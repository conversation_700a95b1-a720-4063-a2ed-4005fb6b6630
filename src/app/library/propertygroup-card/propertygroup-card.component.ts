import { Component, OnInit, Input } from '@angular/core';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'lib-propertygroup-card',
  templateUrl: './propertygroup-card.component.html'
})

export class PropertyGroupCardComponent implements OnInit {
  @Input() propertygroup;

  constructor (
    private router: Router,
    private translateService: TranslateService
  ) { }

  ngOnInit () {
  }

  navigateToPropertyGroup() {
    const url = this.translateService.instant('urls.property-group');
    this.router.navigate([url, this.propertygroup.slug]);
  }
}
