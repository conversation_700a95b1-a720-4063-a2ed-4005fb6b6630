<div *ngIf="loading" class="hero-loader">
  <div class="loading-circle"></div>
</div>

<div class="property-group-hero-cpn property-hero-cpn classic" [class.loading]="loading" *ngIf="propertygroup">
	<div class="swiper js-swiper">
		<div class="swiper-container">

			<div class="swiper-wrapper">
				<div class="swiper-slide">
					<img src="{{ propertygroup.image.fullsize }}" alt="{{ 'library.propertygroup-hero.project' | translate }}, {{ propertygroup.title }}">
				</div>
			</div>

		</div>
	</div>

	<div class="container" *ngIf="propertygroup.price">
		<div class="absolute-ctn">
      
			<p class="price-tag">
				<span class="-inner-title -small">{{ 'library.propertygroup-hero.price-tag-title' | translate }}</span>
				<span class="s -inner">{{ propertygroup.price | currency:'CAD':'symbol-narrow':'2.0-0':this.currentLang }}</span>
			</p>

		</div>
	</div>

</div>