<div class="propertygroup-sheet property-sheet container grid" *ngIf='propertygroup'>
	<div class="property-sheet-content col-12 col-t-lg-8">
		<div class="propertygroup-description">
			<h1 class="title">{{ propertygroup.title }}</h1>
			<p class="subtext" [innerHtml]="propertygroup.description"></p>
		</div>
		<div *ngIf='propertygroup.documents?.length > 0'>
			<lib-property-downloads [documentsList]="propertygroup.documents"></lib-property-downloads>
		</div>
	</div>
	<div class="property-sheet-sidebar col-12 col-t-lg-4">
		<img src="{{ propertygroup.logo_ent.thumbnail }}" alt="Logo {{ propertygroup.title }}">
	</div>
</div>

<div class="neighborhood-photos-cpn container" *ngIf="propertygroup?.medias?.length > 0">
	<h3 class="-h2 -large -center">{{'library.neighborhood-photo.title' | translate}}</h3>

	<!-- Image loader to calculate column heights -->
	<div class="image-wrap" *ngIf="!photos.length">
		<div *ngFor="let photo of tempPhotos" class="img-ctn">
			<img src="{{ photo.path.fullsize }}" (load)="computeImgHeight($event, photo)" alt="{{ photo.media_name }}">
		</div>
	</div>

	<!-- Real image display in computed columns -->
	<div class="image-wrap" *ngIf="photos.length">
		<div *ngFor="let columns of photos" class="img-column">
			<div *ngFor="let photo of columns" class="img-ctn">
				<img src="{{ photo.path.fullsize }}" alt="{{ photo.media_name }}">
			</div>
		</div>
	</div>
</div>
