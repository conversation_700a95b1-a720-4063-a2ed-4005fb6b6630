<div class="search-sell-small-cpn">
	<div class="container">

    <div class="grid">
      <div class="content col-12 col-t-lg-6">
        <span class="subtitle -eyebrow">{{ 'library.search-sell-small.subtitle' | translate }}</span>
        <h2 class="title">{{ 'library.search-sell-small.title' | translate }}</h2>
        <p>{{ 'library.search-sell-small.description' | translate }}</p>
        
        <form class="form-ctn" (ngSubmit)="onSubmitSell()">
          <div class="input-ctn">
            <input type="search" id="address-input" class="large" [(ngModel)]="addressText" [ngModelOptions]="{ standalone: true }"
              placeholder="{{ 'library.search-sell-small.input-placeholder' | translate }}"/>
            <button class="main-button -primary" type="submit"><span>{{ 'library.search-sell-small.button' | translate }}</span></button>
            </div>
        </form>
      </div>

      <div class="image col-12 col-t-lg-6">
        <img src="assets/images/turmel/home_estimation.webp" alt="estimation">
      </div>
    </div>

	</div>
</div>
