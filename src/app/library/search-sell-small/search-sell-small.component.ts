import { Component, OnInit, Renderer2, Inject } from '@angular/core';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { DOCUMENT } from '@angular/common';

import { environment } from '@/../environments/environment';

@Component({
  selector: 'lib-search-sell-small',
  templateUrl: './search-sell-small.component.html'
})

export class SearchSellSmallComponent implements OnInit {
  public address;
  public addressText;
  private GoogleMaps;

  constructor (
    @Inject(DOCUMENT) private document: Document,
    private router: Router,
    private translate: TranslateService,
    private renderer2: Renderer2
  ) {}

  async ngOnInit () {
    // Dynamically inject GoogleMaps script inside DOM - see Evaluation-form component for details
    await this.loadScript('https://maps.googleapis.com/maps/api/js?key=' + environment.gmapToken + '&libraries=places');
    this.GoogleMaps = window.google.maps;

    // Initialize address field with auto-complete
    const addressElement = document.getElementById('address-input');
    const addressOptions = { componentRestrictions: { country: 'ca' } };
    const addressField = new this.GoogleMaps.places.Autocomplete(addressElement, addressOptions);

    addressElement.addEventListener('blur', () => { this.document.querySelector('.pac-container').innerHTML = ''; });

    addressField.addListener('place_changed', () => {
      const address = addressField.getPlace();
      if (address) this.address = address;
    });
  }

  // Inject script element into DOM
  private loadScript (url) {
    // Ignore if script already exists
    if (document.querySelector('script[data-name="gmap-script"]')) return;

    return new Promise((resolve, reject) => {
      const script = this.renderer2.createElement('script');
      script.setAttribute('data-name', 'gmap-script');
      script.type = 'text/javascript';
      script.src = url;
      script.text = '';
      script.async = true;
      script.defer = true;
      script.onload = resolve;
      script.onerror = reject;
      this.renderer2.appendChild(this.document.body, script);
    });
  }

  onSubmitSell () {
    const url = this.translate.instant('urls.real-estate-online-evaluation');
    // Send search string if no Google place is found
    this.router.navigate([url, this.address?.formatted_address || this.addressText]);
  }
}
