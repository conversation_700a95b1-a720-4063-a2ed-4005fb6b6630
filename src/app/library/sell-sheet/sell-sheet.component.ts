import { Component, OnInit } from '@angular/core';
import { BlocksService } from '@/services/v3/contentblocks/contentblocks.service';

@Component({
  selector: 'lib-sell-sheet',
  templateUrl: './sell-sheet.component.html'
})

export class SellSheetComponent implements OnInit {
  public blockTitle: string;
  public blockContent: string;

  constructor (private blocksService: BlocksService) {
    this.blocksService.getBlock('bloc-vendre').subscribe(data => {
      console.log(data);
      this.blockTitle = data.title;
      this.blockContent = data.text;
      document.querySelector('.sell-sheet-page').classList.add('show');
    });
  }

  ngOnInit () {
  }

  ngAfterViewInit() {
    // Get header height and set negative margin on home banner
    const header = document.getElementById('header');
    if (header) {
      const headerHeight = header.offsetHeight;
      const homeBanner = document.querySelector('.hero-landing-cpn') as HTMLElement;
      if (homeBanner) {
        homeBanner.style.top = `-${headerHeight}px`;
        homeBanner.style.marginBottom = `-${headerHeight}px`;
      }
    }
  }
}
