<div class="slider-content-cpn">
    <div class="container">
        <h2 class="title -large">{{ 'library.slider-content.title' | translate }}</h2>
        <div class="swipers-ctn">
            <div class="swiper swiper-content-img">
                <div class="swiper-container">
                    <div class="swiper-wrapper">
                        <!-- Slides -->
                        <div class="swiper-slide">
                            <img src="assets/images/placeholder/slide-01.png">
                        </div>
                        <div class="swiper-slide">
                            <img src="assets/images/placeholder/slide-02.png">
                        </div>
                        <div class="swiper-slide">
                            <img src="assets/images/placeholder/slide-03.png">
                        </div>
                    </div>
                </div>
            </div>
            <div class="grid">
                <div class="col-12 col-t-lg-6 col-offset-t-lg-6">
                    <div class="swiper swiper-content">
                        <div class="swiper-container">
                            <div class="swiper-wrapper">
                                <!-- Slides -->
                                <div class="swiper-slide" *ngFor="let slide of slidesContent; let i = index">
                                    <div class="head">
                                        <h3 class="title">{{slide.title}}</h3>
                                        <div class="step">
                                            <p>{{i + 1}} / {{slidesContent.length}}</p>
                                        </div>
                                    </div>
                                    <p class="content">
                                        {{slide.text}}
                                    </p>
                                </div>
                            </div>
                            <div class="swiper-nav">
                                <div class="swiper-button-prev swiper-btn"></div>
                                <div class="swiper-button-next swiper-btn"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>