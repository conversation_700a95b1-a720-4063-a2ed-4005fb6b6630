/* eslint-disable no-unused-vars */
import { Component, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import Swiper, { Navigation, Pagination, Controller } from 'swiper';
import SwiperCore, { Autoplay } from 'swiper';

@Component({
  selector: 'lib-slider-content',
  templateUrl: './slider-content.component.html'
})

export class SliderContentComponent implements OnInit {
  slidesContent: any;

  constructor (
    private translate: TranslateService
  ) { }

  ngOnInit () {
    this.initSlider();

    this.translate.get('library.slider-content.slides').subscribe(res => {
      this.slidesContent = res;
    });
  }

  initSlider () {
    Swiper.use([Navigation, Pagination, Controller]);
    SwiperCore.use([Autoplay]);
    setTimeout(function () {
      const contentSwiper = new Swiper('.swiper-content .swiper-container', {
        speed: 1000,
        spaceBetween: 60,
        observer: true,
        simulateTouch: false,
        autoplay: {
          delay: 5000
        },
        // autoplayDisableOnInteraction: false,
        runCallbacksOnInit: false,
        navigation: {
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev'
        }
      });

      const imageSwiper = new Swiper('.swiper-content-img .swiper-container', {
        speed: 1000,
        observer: true,
        simulateTouch: false,
        // autoplayDisableOnInteraction: false,
        runCallbacksOnInit: false,
        effect: 'fade'
      });

      contentSwiper.controller.control = imageSwiper;
      imageSwiper.controller.control = contentSwiper;
    }, 1000);
  }
}
