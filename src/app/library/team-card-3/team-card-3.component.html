<div class="team-card-3-cpn container grid" itemscope itemtype="http://schema.org/Person">
	<div class="team-card-details col-12 col-t-lg-7">
		<h1 itemprop="name" class="name" *ngIf="teamMember.firstname && teamMember.lastname">{{teamMember.firstname}} {{teamMember.lastname}}</h1>
		<p itemprop="jobTitle" class="role" *ngIf="teamMember.job">{{teamMember.job}}</p>

		<div itemprop="knowsAbout" *ngIf="teamMember.biography" class="team-card-text" [innerHtml]="teamMember.biography"></div>
		<p itemprop="knowsLanguage" *ngIf="teamMember.languages_value" class="lang">{{ 'library.team-card-1.spoken' | translate }} {{teamMember.languages_value}}</p>
		<div class="social-ctn">
			<a itemprop="telephone" *ngIf="teamMember.phone" href="tel:{{teamMember.phone}}" class="main-button -phone"><i class="icon-mobile"></i> {{teamMember.phone}}</a>
			<a itemprop="email" *ngIf="teamMember.email" href="mailto:{{teamMember.email}}" class="social-icon icon-logo-mail"></a>
			<a *ngIf="teamMember.facebook" target="_blank" href="{{teamMember.facebook}}" class="social-icon icon-logo-facebook"></a>
			<a *ngIf="teamMember.linkedin" target="_blank" href="{{teamMember.linkedin}}" class="social-icon icon-logo-linkedin"></a>
			<a *ngIf="teamMember.instagram" target="_blank" href="{{teamMember.instagram}}" class="social-icon icon-logo-instagram"></a>
			<a *ngIf="teamMember.youtube" target="_blank" href="{{teamMember.youtube}}" class="social-icon icon-logo-youtube"></a>
			<a *ngIf="teamMember.twitter" target="_blank" href="{{teamMember.twitter}}" class="social-icon icon-x"></a>
		</div>
	</div>
	<div class="team-img col-12 col-t-lg-5">
		<ng-template [ngIf]="teamMember.photo" [ngIfElse]="defaultImage">
			<img itemprop="image" src="{{teamMember.photo}}" alt="{{teamMember.firstname}} {{teamMember.lastname}} - {{teamMember.job}}">
		</ng-template>

		<ng-template #defaultImage>
			<img itemprop="image" src="/assets/images/placeholder/default_image.jpeg" alt="{{teamMember.firstname}} {{teamMember.lastname}} - {{teamMember.job}}">
		</ng-template>
	</div>
</div>
