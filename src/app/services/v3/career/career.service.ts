import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { Observable } from 'rxjs';
import { UtilsService } from '@/services/v3/utils/utils.service';

@Injectable()
export class CareerService {
  constructor (
    private translate: TranslateService,
    private utils: UtilsService
  ) {}

  /*
    POST Career
  */
  public postCareer (dataForm): Observable<any> {
    const body = JSON.stringify({
      subject: dataForm.subject,
      firstname: dataForm.firstName,
      lastname: dataForm.lastName,
      address: dataForm.address,
      city: dataForm.city,
      phone: dataForm.phone,
      phone_ext: dataForm.phoneExt,
      email_candidate: dataForm.email,
      email_from: dataForm.email,
      interested_in: dataForm.role,
      attachment: dataForm.file,
      message: dataForm.message,
      language: this.translate.currentLang,
      available_from: dataForm.mydate.formatted,
      token_captcha: dataForm.token_captcha
    });

    return;

    const endpoint = 'contact/career';
    return this.utils.apiPost(endpoint, body);
  }
}
