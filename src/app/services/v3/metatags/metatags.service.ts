import { Injectable } from '@angular/core';
import { Meta, Title } from '@angular/platform-browser';
import { Observable } from 'rxjs';

import { UtilsService } from '@/services/v3/utils/utils.service';

@Injectable()
export class MetatagsService {
  // OG Image default properties
  ogImage = '/assets/images/og-image.png';
  width = 960;
  height = 720;

  constructor (
    private utils: UtilsService,
    private metaService: Meta,
    private titleService: Title
  ) { }

  /*
    Fetch metatags for a page
  */
  private fetchMetatags (page): Observable<any> {
    const endpoint = 'metatags/' + page;
    return this.utils.apiGet(endpoint, response => response.total ? response.data[0] : response);
  }

  /*
    Set canonical and og url
  */
  private setUrl (href) {
    this.metaService.updateTag({ content: href }, 'property="og:url"');
    const canonical = document.querySelector("link[rel='canonical']");
    if (canonical) canonical.setAttribute('href', href);
  }

  /*
    Set title and description
  */
  private setTitleAndDescription (title, desc) {
    this.titleService.setTitle(title);
    this.metaService.updateTag({ content: title }, 'property="og:title"');
    this.metaService.updateTag({ content: desc }, 'name="description"');
    this.metaService.updateTag({ content: desc }, 'property="og:description"');
  }

  /*
    Set og image
  */
  private setImage (image, width = null, height = null) {
    const content = image.includes('http') ? image : window.location.origin + (image || this.ogImage);
    this.metaService.updateTag({ content }, 'property="og:image"');
    if (width) this.metaService.updateTag({ content: width }, 'property="og:image:width"');
    if (height) this.metaService.updateTag({ content: height }, 'property="og:image:height"');
  }

  /*
    Public setter
  */
  public updateMetatags (slugOrData) {
    // Parameter can be either a slug or an object
    const slug = typeof slugOrData === 'string' ? slugOrData : null;

    // Title and description
    if (slug) {
      this.fetchMetatags(slug).subscribe(({ title, desc }) => {
        this.setTitleAndDescription(title, desc);
      });
    } else {
      const { title, desc } = slugOrData || {};
      this.setTitleAndDescription(title, desc);
    }

    // Image and url
    const { image = this.ogImage, width = this.width, height = this.height, url = null } = slug ? {} : slugOrData;
    if (image) this.setImage(image, width, height);

    // URL
    this.setUrl(url || window.location.href);
  }
}
