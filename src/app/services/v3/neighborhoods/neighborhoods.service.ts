import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import { UtilsService } from '@/services/v3/utils/utils.service';

@Injectable()
export class NeighborhoodsService {
  constructor (
    private utils: UtilsService,
    private translate: TranslateService
  ) { }

  /*
    Get a list of neighborhood
  */
  public getNeighborhoods (limit = null, random = null): Observable<any> {
    const endpoint = 'neighborhoods/map' +
      (limit ? 'limit=' + limit + '&' : '') +
      (random ? 'random=' + random : '');

    return this.utils.apiGet(endpoint);
  }

  /*
    Get a single neighborhood by id
  */
  public getNeighborhood (id): Observable<any> {
    const endpoint = 'neighborhood/' + id;
    return this.utils.apiGet(endpoint, ({ data }) => {
      if (data.averages_costs) {
        data.averageCostLabels = data.averages_costs.map(array => array.year);

        // Set Neighborhood average colors
        const colors = [
          { backgroundColor: '#151515', borderColor: '#151515' }, // Green
          { backgroundColor: '#F1002C', borderColor: '#F1002C' } // Dark green
          // { backgroundColor: '#003125', borderColor: '#003125' }, // Dark grey
        ];

        data.averageCostData = [
          {
            data: data.averages_costs.map(array => array.single_family_cost),
            label: this.translate.instant('library.neighborhood-avgcost.cost_unifamiliale'),
            ...colors[0]
          },
          {
            data: data.averages_costs.map(array => array.co_ownership_cost),
            label: this.translate.instant('library.neighborhood-avgcost.cost_copropriete'),
            ...colors[1]
          }
          // {
          //   data: data.averages_costs.map(array => array.multiplex_cost),
          //   label: this.translate.instant('library.neighborhood-avgcost.cost_multiplex'),
          //   ...colors[2]
          // },
        ];
      }
      return { data };
    });
  }

  /*
    Get a single neighborhood by id
  */
  public getNeighborhoodByMLS (mls): Observable<any> {
    const endpoint = 'inscriptions/' + mls + '/neighborhood';
    return this.utils.apiGet(endpoint);
  }

  /*
  Process neighborhood section title to fit d' or de
  */
  public formatTitle(ngbdName, title) {
    const vowels = ['A', 'E', 'É', 'Ê', 'I', 'O', 'U', 'Y'];

    if ( this.translate.instant('global.switchlang') === 'EN' && vowels.includes(ngbdName.charAt(0))) {
      return ( title.slice(0, -1) + "'" + ngbdName );
    } else {
      return ( title + ' ' + ngbdName );
    }
  }
}
