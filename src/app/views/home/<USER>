<lib-header [transparent]="'true'"></lib-header>

<lib-hero-landing 
    [heroImage]="'assets/images/turmel/hero/hero_accueil.webp'"
    [heroImageMobile]="'assets/images/turmel/hero/hero_accueil_mobile.webp'"
    [heroTitle]=" 'views.home.title' | translate "
    [heroButton1]=" 'views.home.btn1' | translate "
    [heroButton2]=" 'views.home.btn2' | translate "
    [heroButton3]=" 'views.home.btn3' | translate "
    [suppClass]="'hero-landing-home'">
</lib-hero-landing>

<!-- Full-width background picture with title/text/cta>team -->
    <lib-cta-broker></lib-cta-broker>

<!-- 3x2 properites grid -->
    <lib-properties-list [properties]="properties" [class]="'home-properties-list'"></lib-properties-list>
    <!-- <lib-properties-slider *ngIf="properties" [properties]="properties"></lib-properties-slider> -->

    <lib-cta-alert-small></lib-cta-alert-small>

    <lib-search-sell-small></lib-search-sell-small>

    <div class="home-customs-cpn">
        <div class="container">
            <div class="grid">
                <div class="col-12 col-t-6 img-ctn">
                    <img src="assets/images/turmel/home_secteurs.webp" alt="home_1" class="img-fluid">
                </div>
                <div class="col-12 col-t-6 text-ctn">
                    <span class="subtitle">
                        {{ 'views.home.custom1.subtitle' | translate }}
                    </span>
                    <h2 class="title">
                        {{ 'views.home.custom1.title' | translate }}
                    </h2> 
                    <p class="text" [innerHTML]="'views.home.custom1.text' | translate"></p>
                    <a [routerLink]="['urls.neighborhoods' | translate ]" class="main-button -primary">
                        {{ 'views.home.custom1.btn' | translate }}
                    </a>
                </div>
            </div>   
        </div>     
        <div class="container">
            <div class="grid -reverse">
                <div class="col-12 col-t-6 img-ctn">
                    <img src="assets/images/turmel/home_projets.webp" alt="home_1" class="img-fluid">
                </div>
                <div class="col-12 col-t-6 text-ctn">
                    <span class="subtitle">
                        {{ 'views.home.custom2.subtitle' | translate }}
                    </span>
                    <h2 class="title">
                        {{ 'views.home.custom2.title' | translate }}
                    </h2> 
                    <p class="text" [innerHTML]="'views.home.custom2.text' | translate"></p>
                    <a [routerLink]="['urls.property-group' | translate ]" class="main-button -primary">
                        {{ 'views.home.custom2.btn' | translate }}
                    </a>
                </div>
            </div>
        </div>
    </div>

<!-- 3 blog posts -->
    <lib-blog-list-5></lib-blog-list-5>

    <lib-googlemybusiness></lib-googlemybusiness>

    <lib-footer></lib-footer>

