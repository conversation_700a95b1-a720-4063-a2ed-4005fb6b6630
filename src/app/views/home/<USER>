import { Component, OnInit, ViewChild } from '@angular/core';

import { InscriptionsService } from '@/services/v3/inscriptions/inscriptions.service';
import { MetatagsService } from '@/services/v3/metatags/metatags.service';
import { PropertiesListComponent } from '@/library/properties-list/properties-list.component';
import { HomePageService } from '@/services/v3/home-page-block/home-page-block.service';

@Component({
  selector: 'view-home',
  templateUrl: './home.component.html'
})

export class HomeComponent implements OnInit {
  @ViewChild(PropertiesListComponent)
  private propertiesListComponent: PropertiesListComponent;

  customContent: any;
  properties: any;

  constructor (
    private homePageService: HomePageService,
    private inscriptionsService: InscriptionsService,
    private metatagsService: MetatagsService
  ) {
    this.metatagsService.updateMetatags('home');

    this.homePageService.getHomePage().subscribe(({ data }) => {
      this.customContent = data;
      // console.log(this.customContent)
    });
  }

  ngOnInit () {
    const search = { sold: 0, sort: "newest" };
    // if you need to get featured and random properties
    // { featured: 1, sort: 'rand' }
    this.inscriptionsService.getInscriptions(6, search).subscribe(({ data }) => {
      if (data.length > 0) {
        this.properties = data;

        if (this.propertiesListComponent) {
          this.propertiesListComponent.properties = data;
        }
      }
    });
  }

  ngAfterViewInit() {
    // Get header height and set negative margin on home banner
    const header = document.getElementById('header');
    if (header) {
      const headerHeight = header.offsetHeight;
      const homeBanner = document.querySelector('.hero-landing-cpn') as HTMLElement;
      if (homeBanner) {
        homeBanner.style.top = `-${headerHeight}px`;
        homeBanner.style.marginBottom = `-${headerHeight}px`;
      }
    }
  }
}
