import { Component, OnInit, ViewChild } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';

import { NeighborhoodHeroComponent } from '@/library/neighborhood-hero/neighborhood-hero.component';
import { NeighborhoodListComponent } from '@/library/neighborhood-list/neighborhood-list.component';
import { NeighborhoodMap2Component } from '@/library/neighborhood-map-2/neighborhood-map-2.component';

import { NeighborhoodsService } from '@/services/v3/neighborhoods/neighborhoods.service';
import { InscriptionsService } from '@/services/v3/inscriptions/inscriptions.service';
import { MetatagsService } from '@/services/v3/metatags/metatags.service';

@Component({
  selector: 'view-neighborhood',
  templateUrl: './neighborhood.component.html'
})

export class NeighborhoodComponent implements OnInit {
  public currentId: any;
  public neighborhood: any;
  public previousNav: any;
  public nextNav: any;
  public navigationNeighborhood: any;
  public properties: any;
  public formatTitle: Function;

  @ViewChild(NeighborhoodListComponent)
  private neighborhoodListComponent: NeighborhoodListComponent;

  @ViewChild(NeighborhoodHeroComponent)
  private neighborhoodHeroComponent: NeighborhoodHeroComponent;

  @ViewChild(NeighborhoodMap2Component)
  private neighborhoodMap2Component: NeighborhoodMap2Component;

  constructor (
    private router: Router,
    private neighborhoodService: NeighborhoodsService,
    private inscriptionsService: InscriptionsService,
    private route: ActivatedRoute,
    private translate: TranslateService,
    private metatagsService: MetatagsService
  ) {
    this.currentId = this.route.snapshot.paramMap.get('slug');
    this.neighborhoodService.getNeighborhood(this.currentId).subscribe(({ data }) => {
      if (!data || data.length === 0) {
        this.router.navigateByUrl(this.translate.instant('urls.home'));
      } else {
        this.neighborhood = data;
        this.previousNav = data.prev;
        this.nextNav = data.next;

        // Use meta data if available
        const title = data.meta_title || data.name;
        const desc = data.meta_description || data.description.replace(/<[^>]*>/g, '');
        const image = data.header_image;
        this.metatagsService.updateMetatags({ title, desc, image });

        //this.neighborhoodHeroComponent.initVideo(this.neighborhood);

        setTimeout(() => { this.neighborhoodMap2Component.initMap(this.neighborhood); }, 150);

        this.getProperties();
      }
    });

    this.router.routeReuseStrategy.shouldReuseRoute = function () {
      return false;
    };

    this.router.events.subscribe((evt) => {
      if (evt instanceof NavigationEnd) {
        // trick the Router into believing it's last link wasn't previously loaded
        this.router.navigated = false;
      }
    });

    // Bind formatTitle
    this.formatTitle = this.neighborhoodService.formatTitle.bind(this.neighborhoodService);
  }

  ngOnInit () {
  }

  getProperties () {
    const search = {
      featured: false,
      sort: 'rand',
      neighborhood: this.neighborhood.slug
    };

    this.inscriptionsService.getInscriptions(6, search).subscribe(data => {
      this.properties = data.data;
      this.neighborhoodListComponent.properties = data;
    });
  }

  ngAfterViewInit() {
    // Get header height and set negative margin on home banner
    const header = document.getElementById('header');
    if (header) {
      const headerHeight = header.offsetHeight;
      const homeBanner = document.querySelector('.neighborhood-hero-cpn') as HTMLElement;
      if (homeBanner) {
        homeBanner.style.top = `-${headerHeight}px`;
        homeBanner.style.marginBottom = `-${headerHeight}px`;
      }
    }
  }
}
