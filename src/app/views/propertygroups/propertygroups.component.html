<lib-header></lib-header>
<div class="propertygroups-page">
	<div class="property-group-head container">
		<h1 class="page-title -display">{{ 'views.propertygroups.title' | translate }}</h1>
		<p class="page-description">{{ 'views.propertygroups.description' | translate }}</p>
	</div>
	<div class="property-group-card-ctn container">
		<div class="propertygroup-card-cpn" *ngFor="let propertygroup of propertygroups">
			<lib-propertygroup-card [propertygroup]="propertygroup"></lib-propertygroup-card>
		</div>
	</div>

	<div class="property-group-cta">
		<a class="main-button -primary" [routerLink]="['urls.contact' | translate]">{{ 'views.propertygroups.cta' | translate }}</a>
	</div>
</div>

<lib-footer></lib-footer>
