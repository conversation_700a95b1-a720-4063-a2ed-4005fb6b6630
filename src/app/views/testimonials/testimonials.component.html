<lib-header></lib-header>

<div class="testimonials-page -space-medium">
	<div class="container">

		<div class="testimonials-head -page-head">
			<h1 class="-center -small">{{ 'views.testimonials.title' | translate }}</h1>
		</div>

		<div class="grid-ctn">
			<div class="testimonials-wrap">
				<div class="testimonial-card-cpn" id="testimonial-list" *ngFor="let testimonial of testimonials | paginate: { id: 'testimonial-pagination', itemsPerPage: 8, currentPage: cPage}">
					<lib-testimonials-card [testimonial]="testimonial"></lib-testimonials-card>
				</div>
			</div>
		</div>

		<pagination-controls id="testimonial-pagination" class="paginiation-controls"
            (pageChange)="onPageChange($event)"
			maxSize="5"
			directionLinks="true"
			previousLabel=""
			nextLabel=""
			autoHide="true">
		</pagination-controls>

	</div>
</div>

<lib-footer></lib-footer>