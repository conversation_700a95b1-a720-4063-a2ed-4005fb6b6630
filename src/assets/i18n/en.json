{"client": {"name": "<PERSON>", "phone": "************", "email": "<EMAIL>", "email-cta": "Write us", "address": "8165 1re Av.", "city": "Québec, QC G1G 4B8", "facebook": "https://www.facebook.com/courtiersimmobiliers/", "twitter": "https://www.twitter.com", "instagram": "https://www.instagram.com/francisturmelcourtiers/", "linkedin": "https://www.linkedin.com", "youtube": "https://www.youtube.com", "map": {"url": "https://maps.app.goo.gl/QvFiv4udsULgreYt5", "coords": [46.861774, -71.271077, 11], "color": "#151515", "soldColor": "#D61616", "neighborhood": {"lat": 45.501689, "lng": -73.567256, "zoom": 10}}, "maxDistance": 0}, "global": {"write-us": "Write us", "advanced-search": "Advanced search", "all": "All", "all-properties": "All properties", "all-our-properties": "See all our properties", "btn-video": "See the video", "buy": "Buy", "contact": "Contact us", "evaluate-online": "Online evaluation", "new-projects": "Our investment", "next": "Next", "prev": "Previous", "learn-more": "Learn more", "read-more": "Read more", "search": "Search", "sell": "for sale", "sale": "sale", "rent": "for rent", "rental": "rental", "property": "property", "properties": "properties", "subscribe-alert": "Subscribe to our real estate alert", "switchlang": "FR", "testimonials": "Testimonials"}, "urls": {"aboutus": "/en/aboutus", "buy-house": "/en/become-an-owner", "contact": "/en/contact", "contest": "/en/contest", "career": "/en/career", "neighborhoods": "/en/neighborhoods", "districts": "/en/neighborhoods", "favorites": "/en/my-favorites", "property-group": "/en/real-estate-projects", "home": "/en", "home-staging": "/en/home-staging", "landing": "/en/landing", "open-house": "/en/open-house", "property-single": "/en/property", "real-estate-agents": "/en/real-estate-agents", "real-estate-agents-2": "/en/real-estate-agents-2", "real-estate-agents-3": "/en/real-estate-agents-3", "real-estate-agents-4": "/en/real-estate-agents-4", "real-estate-agents-5": "/en/real-estate-agents-5", "real-estate-agents-6": "/en/real-estate-agents-6", "real-estate-alert": "/en/real-estate-alert", "real-estate-blog": "/en/blog-real-estate-news", "real-estate-online-evaluation": "/en/online-property-evaluation", "sell-house": "/en/sales-strategy", "search-properties": "/en/properties-for-sale", "specialists": "/en/our-specialists", "testimonials": "/en/testimonials", "privacy-policy": "/en/privacy-policy", "translate": {"/en": "/fr", "/en/aboutus": "/fr/a-propos", "/en/become-an-owner": "/fr/devenir-proprietaire", "/en/contact": "/fr/contact", "/en/contest": "/fr/concours", "/en/career": "/fr/carriere", "/en/neighborhoods": "/fr/secteurs", "/en/my-favorites": "/fr/mes-favoris", "/en/real-estate-projects": "/fr/investissement", "/en/home-staging": "/fr/home-staging", "/en/landing": "/fr/campagne", "/en/open-house": "/fr/visites-libres", "/en/real-estate-agents": "/fr/courtiers-immobiliers", "/en/real-estate-agents-2": "/fr/courtiers-immobiliers-2", "/en/real-estate-agents-3": "/fr/notre-equipe", "/en/real-estate-agents-4": "/fr/courtiers-immobiliers-4", "/en/real-estate-agents-5": "/fr/courtiers-immobiliers-5", "/en/real-estate-agents-6": "/fr/courtiers-immobiliers-6", "/en/real-estate-alert": "/fr/alerte-immobiliere", "/en/blog-real-estate-news": "/fr/blogue-immobilier", "/en/online-property-evaluation": "/fr/estimation-immobiliere-en-ligne", "/en/sales-strategy": "/fr/strategie-de-vente", "/en/properties-for-sale": "/fr/liste-proprietes-a-vendre", "/en/our-specialists": "/fr/nos-partenaires", "/en/testimonials": "/fr/temoignages", "/en/property": "/fr/propriete", "/en/privacy-policy": "/fr/politique-de-confidentialite"}}, "library": {"alert-cta-1": {"real-estate-alert": "Real estate alert", "subscribe-to-our-free": "Subscribe to our free real estate alert and do not miss any opportunity by receiving all the news that meet your search criteria as soon as they are posted!", "subscribe": "Subscribe"}, "alert-cta-2": {"real-estate-alert": "Real estate alert", "subscribe-to-our-free": "Subscribe to our free real estate alert and do not miss any opportunity by receiving all the news that meet your search criteria as soon as they are posted!", "subscribe": "Subscribe to the real estate alert"}, "alert-form": {"title": "Real estate alert", "description": "Fill out the form to subscribe for free to our real estate alert and get notified as soon as a new property meeting your search criteria gets posted.", "step1": {"title": "Where would you like to buy?"}, "step2": {"title": "What type of property are you looking for?", "other_label": "Other?", "budget_label": "What is your approximate budget?"}, "step3": {"title": "Tell us how to reach you", "firstname": "First name *", "name": "Last name *", "phone": "Phone *", "email": "Email *"}, "warning_message": "By clicking on \"Send\", you confirm that you are interested in being contacted by our team.", "submit-message": "Your subscription to the real estate alert has been completed.", "back": "Back", "required": "(* Required fields)", "errors": {"firstname_required": "First name is required", "lastname_required": "Last name is required", "phone_required": "Phone number is required", "phone_invalid": "Invalid phone number", "email_invalid": "Invalid email address", "email_required": "Email address is required", "message_required": "A message is required", "submit-message": "An error has occured while submitting your information. Please try again later."}, "submit": "Send", "next": "Next", "before": "Previous", "bungalow": "Bungalow", "semi-detached-bungalow": "Semi-detached bungalow", "undivided-co-ownership": "Undivided co-ownership", "semi-detached-cottage": "Semi-detached cottage", "income-property": "Income property", "commercial-building": "Commercial building", "townhouse": "Townhouse", "quadruplex": "Quadruplex and more", "split-level": "Split level", "plot": "Plot", "business-sale": "Business sale", "select": "Select"}, "article": {"alt-image": "Read post "}, "awards-slider": {"title": "Our awards"}, "blog-details": {"back": "Back to list", "all-articles": "See all articles", "contact-cta": {"title": "Is this a topic of interest?", "description": "For more information on this subject or to discuss it with experts, do not hesitate to contact us.", "button": "Contact us"}}, "blog-list-1": {"title": "From our blog", "all-posts": "All posts"}, "blog-list-2": {"title": "Blog", "all": "All"}, "blog-list-3": {"title": "Blog", "all": "All"}, "blog-list-4": {"you-will-also-like": "You will also like", "all-posts": "All posts"}, "custom-content": {"title": "Custom content", "text": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Duis mattis sagittis tristique. Phasellus eget rhoncus tortor, et pulvinar magna vestibulum tincidunt.", "learn-more": "Learn more"}, "broker-contact-header-1": {"title": "L’équipe <PERSON>", "see-maps": "See on the map", "see-direction": "Directions", "alt-image": "real estate broker", "description": "Lorem ipsum dolor sit amet, consectetur adipisicing elit. Eos maiores nihil voluptates ab quidem deleniti praesentium, omnis nobis quibusdam voluptate eius quaerat, facilis hic accusantium expedita optio dicta doloremque! Facere."}, "broker-contact-header-2": {"title": "Team of e-closion", "see-maps": "See on the map", "see-direction": "Directions", "alt-image": "real estate broker", "description": "Lorem ipsum dolor sit amet, consectetur adipisicing elit. Eos maiores nihil voluptates ab quidem deleniti praesentium, omnis nobis quibusdam voluptate eius quaerat, facilis hic accusantium expedita optio dicta doloremque! Facere."}, "broker-contact-form": {"firstname": "First name *", "lastname": "Last name *", "address": "Address", "city": "City", "phone": "Phone *", "email": "Email *", "successMessage": "We will contact you as soon as possible.", "successTitle": "Thank you!", "errorMessage": "An error has occured. Please try again later.", "submit": "Submit", "back": "Back", "subject": "Contact us", "subject-landing": "Landing : ", "errors": {"firstname_required": "First name is required", "lastname_required": "Last name is required", "city_required": "City is required", "phone_required": "Phone is required", "phone_invalid": "Invalid phone number", "email_invalid": "Invalid email", "email_required": "Email is required", "mydate_required": "Start date is required", "role_required": "Select at least one item", "message_required": "This field is required"}}, "broker-contact-list": {"title-brokers": "Real estate brokers", "title-administrators": "Administrators", "button": "Learn more about the team"}, "document-1": {"title": "Buyer guide", "description": "Lorem ipsum dolor sit amet, consectetur adipisicing elit. Eos maiores nihil voluptates ab quidem deleniti praesentium, omnis nobis quibusdam voluptate eius quaerat, facilis hic accusantium expedita optio dicta doloremque! Facere.", "download": "Download", "download-link": "https://admin.e-closion.ca/uploads/reports/how-to-successfully-purchase-a-new-property-57d6cbebd347b.pdf"}, "buy-guide-cta": {"title": "Practical guide for buying a property", "alt-image": "Buyer's guide pdf document", "description": "In this comprehensive buyer's guide, you will find a wealth of relevant information and valuable advice to help you make a successful transaction.", "download": "Consult", "download-link": "https://www.oaciq.com/en/general-public/buying/buyers-guide/"}, "sell-guide-cta": {"title": "Practical guide for selling a property", "alt-image": "<PERSON><PERSON>'s guide pdf document", "description": "In this comprehensive seller's guide, you will find a wealth of relevant information and valuable advice to help you make a successful transaction.", "download": "Consult", "download-link": "https://www.oaciq.com/en/general-public/residential-sale/sellers-guide/"}, "buy-sheet": {"title": "Your project deserves a team that’s present, genuine, and committed", "description": "Buying a property isn’t just about signing papers — it’s about making a life choice. It’s choosing the neighborhood where your children will grow up, the home where you’ll welcome family and friends, the place where you’ll truly feel at home. And in many cases, it’s the biggest investment of your life. <br><br>That’s why your project matters to us. Every member of our team is fully committed to helping you make the best possible decision — the kind of decision that will make you proud to recommend us to your loved ones. That’s our goal.", "button": "Contact us", "subtitle": "Pourquoi acheter avec l’équipe e-closion?", "list": {"item-1": "Vestibulum tincidunt eget rhoncus tortor", "item-2": "Vestibulum tincidunt eget rhoncus tortor", "item-3": "Vestibulum tincidunt eget rhoncus tortor"}, "team": {"title": "Vous désirez acheter une propriété?", "description": "Communiquez avec nous dès maintenant pour rencontrer un courtier de notre équipe.", "btn": "Nous joindre"}}, "reviews": {"undertitle": "Google reviews", "title": "Our latest Google reviews"}, "career-contact": {"date-format": "mm/dd/yyyy", "subject": "Career application", "title": "Career", "description": "Are you passionate by real estate? Do you want to join a dynamic team that will recognize your talent and your vision to achieve your ambitions and expand your field of action? Contact us to see how we could collaborate!", "required": "(* Required fields)", "subtitle1": "Please provide more information", "subtitle2": "Tell us how to reach you", "step1": {"mydate-label": "Available start date? *", "upload": "Attach a file *", "poste-type-label": "Type of job that interests you? *", "cv-label": "Join your CV *", "message-label": "Message *", "select": "Select", "roles": [{"id": 1, "name": "Real estate broker"}, {"id": 2, "name": "Administration"}]}, "step2": {"firstname": "First name *", "lastname": "Last name *", "address": "Address", "city": "City", "phone": "Phone *", "email": "Email *"}, "successMessage": "Your application has been sent successfully", "errorMessage": "An error has occured. Please try again later", "errors": {"firstname_required": "First name is required", "lastname_required": "Last name is required", "city_required": "City is required", "phone_required": "Phone is required", "phone_invalid": "Invalid phone number", "email_invalid": "Invalid email", "email_required": "Email is required", "mydate_required": "The date is required", "role_required": "Select at least one item", "message_required": "This field is required"}, "submit": "Submit", "next": "Next", "return": "Previous", "learn-more-about-the-team": "Learn more about the team", "real-estate-brokers": "Real estate brokers", "back": "Back"}, "cta-broker": {"eyebrow": "L'équipe <PERSON>", "title": "Slogan de l’équipe", "subtext": "Morbi ultricies id ipsum sed suscipit. Sed gravida semper ipsum quis finibus. <PERSON><PERSON>ce lobortis enim at turpis ullamcorper congue. Duis consequat mi sem, at commodo mauris laoreet id. Duis tincidunt tempor posuere.", "list": {"item-1": "Vestibulum tincidunt eget rhoncus tortor", "item-2": "Vestibulum tincidunt eget rhoncus tortor", "item-3": "Vestibulum tincidunt eget rhoncus tortor"}, "broker-name": "<PERSON>", "broker-role": "Courtier immobilier ag<PERSON><PERSON>", "button": "Meet our team", "alt-image": "courtier immobilier"}, "cta-centered": {"title": "Real estate alert", "subtext": "Subscribe for free to our real estate alert and get notified as soon as a new property meeting your search criteria gets posted.", "btn": "Subscribe to our real estate alert"}, "cta-estimation": {"title": "How much is my property worth ?", "subtext": "Receive a free assessment of your property's market value.", "btn": "Get my free property assessment"}, "cta-evaluation-small": {"title": "How much is my property worth ?", "subtext": "Receive a free assessment of your property's market value.", "btn": "Get my free property assessment"}, "cta-evaluation": {"title": "How much is my property worth ?", "subtext": "Get an assessment of the market value of your property quickly.", "btn": "Get my free assessment"}, "cta-alert": {"title": "Real estate alert", "subtext": "Subscribe for free to our real estate alert and get notified as soon as a new property meeting your search criteria gets posted.", "btn": "Subscribe to our real estate alert"}, "cta-alert-small": {"title": "Property alert", "subtext": "Sign up for our free property alert. Become a VIP buyer and never miss an opportunity — receive new listings that match your search criteria the moment they hit the market.", "btn": "Sign up for property alerts"}, "evaluation-form": {"title": "How much is my property worth?", "description": "Fill out the following form to receive a free assessment of your property's market value.", "placeholder": "Start typing the address of your property", "btn-evaluation": "Get my free property assessment", "evaluation-image-weight": "Maximum weight of 2 MB per photo (total weight of 16 MB)", "form": {"title": "Property assessment", "property-found": "Here's what we found", "property_about": "About your property (optional)", "property_about_2": "About your property (optional)", "no-view": "Street view is not available for this address", "no-result": "Street view is not available for this address", "property_found": "We found your property!", "edit": "Edit", "placeholder_choose": "Choose an option", "emphase-title": "Tell us how to reach you", "submit-validation": "By clicking on \"Send\", you confirm that you are interested in being contacted by our team.", "submit": "Submit my evaluation request", "submit-message": "Your request for online evaluation has been taken into account! We will deal with it as soon as possible.", "submit-another": "Submit a new evaluation request", "required": "(* Required fields)", "too-far-alert": {"title": "It sounds like you're outside our service area, which covers Grand Montreal.", "description": "We recommend that you find a local broker who can better meet your needs."}, "label": {"firstname": "First name", "lastname": "Last name", "phone": "Phone", "email": "Email", "type": "Property type", "years": "Year of construction", "surface": "Approximate habitable area", "surface-unit": "Unit for the habitable area", "dimension": "Lot dimensions", "dimension-unit": "Unit for the lot dimensions", "upload-btn": "Join some images", "upload": "Upload photos of your property", "filesize": "Maximum size of 2 Mo per file (total size of 16 Mo)", "bedroom": "Bedrooms", "bathroom": "Bathrooms", "pools": "Swimming pools", "basements": "Basements", "car_park": "Outdoor parking spaces", "garages": "Garages", "buy_reason": "Why are you thinking of selling?", "renovation": "What are the renovations and major improvements to your property, the year they were made and related costs?", "goal": "What is the purpose of your request?", "move-delay": "What's the ideal time for your move?", "evaluate-agree": "Has an approved assessment been done on your property in the last 5 years?", "duration-market": "How long are you ready to put your property on the market?", "yes": "Yes", "yes-alt": "Yes, more than 5 years ago", "error": "File size is too large", "too-far": "I understand that I'm outside the area served. I would still like to be contacted, as my situation is special."}, "surface_values": [{"id": "1", "name": "0 to 500 sq. ft"}, {"id": "2", "name": "500 to 750 sq. ft"}, {"id": "3", "name": "750 to 1000 sq. ft"}, {"id": "4", "name": "1000 to 1250 sq. ft"}, {"id": "5", "name": "1250 to 1500 sq. ft"}, {"id": "6", "name": "1500 to 1750 sq. ft"}, {"id": "7", "name": "1750 to 2000 sq. ft"}, {"id": "8", "name": "2000 to 2250 sq. ft"}, {"id": "9", "name": "2250 to 2500 sq. ft"}, {"id": "10", "name": "2500 to 2750 sq. ft"}, {"id": "11", "name": "2750 to 3000 sq. ft"}, {"id": "12", "name": "3000+ sq. ft"}], "dimension_unit_values": [{"id": "1", "name": "<PERSON><PERSON>s"}, {"id": "2", "name": "hectares"}, {"id": "3", "name": "Square meters"}, {"id": "4", "name": "Square feet"}], "basement_values": [{"id": "1", "name": "None"}, {"id": "2", "name": "Not finished"}, {"id": "3", "name": "Partially finished"}, {"id": "4", "name": "Finished"}], "swimmingpool_values": [{"id": "1", "name": "None"}, {"id": "2", "name": "Aboveground"}, {"id": "3", "name": "In-ground"}], "demand_goal_values": [{"id": "1", "name": "Sale of the property"}, {"id": "2", "name": "Refinancing of the property"}, {"id": "3", "name": "Simply knowing the market value"}, {"id": "4", "name": "Other"}], "move_delay_values": [{"id": "1", "name": "Now"}, {"id": "2", "name": "Within 3 months"}, {"id": "3", "name": "Between 3 and 6 months"}, {"id": "4", "name": "Between 6 months and 1 year"}, {"id": "5", "name": "1 year and over"}], "dimension_values": [{"id": "1", "name": "0 to 3000 sq. ft"}, {"id": "2", "name": "3000 to 5000 sq. ft"}, {"id": "3", "name": "5000 to 10000 sq. ft"}, {"id": "4", "name": "10000+ sq. ft"}], "errors": {"constructYear_invalid": "field invalid", "submit-message": "An error has occured. Please try again later.", "back": "Back", "firstname_required": "First name is required", "lastname_required": "Last name is required", "phone_required": "Phone is required", "phone_invalid": "Invalid phone number", "email_invalid": "Invalid email", "email_required": "Email is required"}}}, "footer": {"properties": "Properties", "search-properties": "Property search", "property-groups": "Investment", "must-see-neighborhoods": "Must-see neighbourhoods", "blog": "Blog", "stats": "Statistics", "news": "News", "buy": "Buy", "alert": "Real estate alert", "buying-tips": "Become an owner", "open-house": "Open house", "sell": "<PERSON>ll", "evaluate-online": "Property assessment", "selling-tips": "Sales strategy", "home-staging": "Homestaging", "team": "Team", "our-team": "Our team", "specialists": "Our specialists", "testimonials": "Testimonials", "career": "Career", "contact": "Contact us", "privacy-policy": "Privacy policy", "role": "Real estate agency. Independent and independent graduate <PERSON><PERSON><PERSON><PERSON> 2000", "copyright-bis": "Web design and digital marketing for real estate brokers"}, "header": {"home": "Home", "team": "Team", "our-team": "Our team", "our-team-2": "Our team (2)", "our-team-3": "Our team", "our-team-4": "Our team (4)", "our-team-5": "Solo broker", "our-team-6": "Small team", "specialists": "Our specialists", "testimonials": "Testimonials", "career": "Career", "neighborhoods": "Sectors", "properties": "Properties", "search-properties": "Property search", "last-inscriptions": "Last inscriptions", "property-groups": "Investment", "must-see-neighborhoods": "Must-see neighbourhoods", "buy": "Buy", "alert": "Real estate alert", "buying-tips": "Become an owner", "open-house": "Open house", "sell": "<PERSON>ll", "evaluate-online": "Property assessment", "selling-tips": "Sales strategy", "home-staging": "Home staging", "blog": "Blog", "stats": "Statistics", "news": "News", "contact": "Contact us", "alt-image": "home page"}, "home-hero-2": {"main-title": "Lorem ipsum dolor sit amet consectetur"}, "home-hero-3": {"text": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris.", "btn": "See more"}, "homestaging-card": {"before": "Before", "before-alt": "State of repair before renovation", "after": "after", "after-alt": "State of repair after renovation", "alt": "Home staging realisation"}, "homestaging-cta": {"title": "Home staging", "text": "Home staging has a proven track record and significantly influences the speed of a sale. Love at first sight will increase the price you will get for your property. You can maximize the potential of your condo, or a more affordable home, by decluttering and refreshing the premises.", "btn": "Learn more", "alt-image": "Home stagin: sell your house"}, "homestaging-sheet": {"title": "Home staging", "text": "Ut eu ipsum sit amet enim fermentum rutrum in eu felis. Maecenas vel massa nec turpis imperdiet tempus. Aenean tristique convallis odio eu ornare. Sed feugiat lorem at nibh venenatis, congue molestie lectus fermentum. Ut sit amet iaculis elit, et finibus elit. Integer eu vulputate urna, vitae sagittis felis. Integer lobortis vulputate nisi vitae vestibulum.", "subtitle": "Why do business with a professional?", "list": {"item-1": "Vestibulum tincidunt eget rhoncus tortor", "item-2": "Vestibulum tincidunt eget rhoncus tortor", "item-3": "Vestibulum tincidunt eget rhoncus tortor", "item-4": "Vestibulum tincidunt eget rhoncus tortor"}, "team": {"title": "Would you like to get a free consultation?", "text": "Contact us for expert advice to maximize the value of your property.", "btn": "Contact us"}}, "homestaging-slider": {"title": "Home staging", "before": "before", "after": "after", "text": "You will never have a second chance to make a good first impression. Home staging has a proven track record and significantly influences the speed of a sale. It also favors the favorites, which will increase the price you will get for your property. In addition, home staging is not reserved for luxury properties. You can maximize the potential of your condo, or a more affordable home, by refining and refreshing the premises. Our team offers all our clients a free consultation and provides a detailed report on the development of your property.", "infobox": {"title": "Would you like to get a free consultation?", "description": "Contact us for expert advice to maximize the value of your property.", "button": "Contact us"}}, "landing-footer": {}, "landing-header": {}, "landing-sheet": {}, "instagram-gallery": {"title": "Instagram"}, "map-pane": {"per-month": " / month"}, "openhouses-list": {"title": "Visites libres"}, "panel": {"menu": {"team": "Team", "our-team": "Our team", "specialists": "Specialists", "testimonials": "Testimonials", "properties": "Properties", "search-properties": "All properties", "property-groups": "Investment", "buy": "Buy", "real-estate-alert": "Real estate alert", "buying-tips": "Become an owner", "open-house": "Open house", "sell": "<PERSON>ll", "evaluate-online": "Free assessment", "selling-tips": "Sales strategy", "home-staging": "Homestaging", "blog": "Blog", "contact": "Contact", "neighborhoods": "Discover the neighborhoods"}}, "prog-list": {"main-title": "Re/max programs", "alt-image": "See in detail", "prog-tranquili-t": {"title": "Tranquilli-t program", "icon": "tranquilli-t.svg", "text": "The exclusive Tranquilli-T protection from RE/MAX, for your peace of mind.", "link": "https://www.remax-quebec.com/en/tranquilli-t/index.rmx"}, "prog-integri-t": {"title": "Integri-t program", "icon": "integri-t-en.svg", "text": "The Integri-T program is an exclusive guarantee from RE/MAX in the event of latent defects.", "link": "https://www.remax-quebec.com/en/integri-t/index.rmx"}, "prog-securi-t": {"title": "Securit-t", "icon": "securi-t-en.svg", "text": "Securi-T covers your mortgage payments in case you lose your job.", "link": "https://www.remax-quebec.com/en/securi-t/index.rmx"}}, "properties": {"sold": "Sold", "new": "New", "visit": "Visit on", "per-month": " / month", "alt-image": "See the property ", "rental-possible": "Location possible"}, "properties-featured": {"subtitle": "Recent listings", "title": "Featured properties!", "text": "Discover our latest listings on the market. Whether you're looking for a first home, a family house, or an exceptional property, find the one that meets your criteria right here.", "btn": "All our properties"}, "properties-sold": {"title": "Properties sold"}, "properties-list": {"title": "New properties", "all": "All our properties"}, "properties-list-2": {"title": "Others interesting properties", "all": "All our properties"}, "properties-slider": {"title": "Featured properties", "all": "All properties"}, "properties-open-house": {"title": "Open house", "visit-from": "From", "visit-to": "to"}, "property-addenda": {"title": "Addenda", "btn": "See more"}, "property-broker-card-1": {"contact": "Contact", "share": "Share"}, "property-characteristics": {"title": "Characteristics", "btn": "See more characteristics"}, "property-details": {"title": "Property details", "type": "Building type", "living-area": "living area", "dimensions-land": "Lot size", "surface-land": "Area size", "land-register": "Cadastral", "building-area": "Building area", "zonage": "Zoning", "construct-year": "Construction date", "financial-recovery": "Financial recovery", "label-yes": "Yes", "label-no": "No", "location-certificate": "Location certificate", "apt": "apt"}, "property-downloads": {"title": "Downloadable documents", "button": "Download", "document-title": "Technical sheet"}, "property-form-contact": {"firstname": "First name *", "lastname": "Last name *", "phone": "Phone *", "email": "Email *", "message": "Message *", "submit": "Submit", "placeholder-date": "Request an appointment ?", "placeholder-time": "Choose an hour", "subject": "Contact request for a property", "submit-title": "Thank you!", "submit-text": "We will contact you as soon as possible.", "back": "Back to property", "retry": "Retry", "errors": {"firstname_required": "First name is required", "lastname_required": "Last name is required", "phone_required": "Phone is required", "phone_invalid": "Invalid phone number", "email_invalid": "Invalid email", "email_required": "Email is required", "message_required": "This field is required", "errorMessage": "An error has occured. Please try again later."}, "hours": [{"name": "7:00 AM", "value": "7:00:00"}, {"name": "7:30 AM", "value": "7:30:00"}, {"name": "8:00 AM", "value": "8:00:00"}, {"name": "8:30 AM", "value": "8:30:00"}, {"name": "9:00 AM", "value": "9:00:00"}, {"name": "9:30 AM", "value": "9:30:00"}, {"name": "10:00 AM", "value": "10:00:00"}, {"name": "10:30 AM", "value": "10:30:00"}, {"name": "11:00 AM", "value": "11:00:00"}, {"name": "11:30 AM", "value": "11:30:00"}, {"name": "12:00 PM", "value": "12:00:00"}, {"name": "12:30 PM", "value": "12:30:00"}, {"name": "1:00 PM", "value": "13:00:00"}, {"name": "1:30 PM", "value": "13:30:00"}, {"name": "2:00 PM", "value": "14:00:00"}, {"name": "2:30 PM", "value": "14:30:00"}, {"name": "3:00 PM", "value": "15:00:00"}, {"name": "3:30 PM", "value": "15:30:00"}, {"name": "4:00 PM", "value": "16:00:00"}, {"name": "4:30 PM", "value": "16:30:00"}, {"name": "5:00 PM", "value": "17:00:00"}, {"name": "5:30 PM", "value": "17:30:00"}, {"name": "6:00 PM", "value": "18:00:00"}, {"name": "6:30 PM", "value": "18:30:00"}, {"name": "7:00 PM", "value": "19:00:00"}, {"name": "7:30 PM", "value": "19:30:00"}, {"name": "8:00 PM", "value": "20:00:00"}]}, "property-favorite": {"save-item": "Save to favorites", "remove-item": "Remove from favorites"}, "propertygroup-card": {"link": "More information", "alt-image": "See the real estate project in detail"}, "propertygroup-hero": {"project": "Real estate project", "price-tag-title": "From"}, "propertygroup-navigation": {"alt-image-prev": "See the previous project", "previous-property": "Previous project", "alt-image-next": "See the next project", "next-property": "Next project"}, "property-hero": {"see-photos": "See photos", "sold": "V<PERSON><PERSON>", "per-month": " / month", "commercial-year": " / year", "commercial-month": " / month", "virtual-visit": "Virtual visit", "see-3d": "3D visit", "see-video": "Video visit", "see-air": "Aerial view", "see-other": "Other visit"}, "property-inclusion": {"inclusion": "Inclusions", "exclusion": "Exclusions"}, "property-map": {"location-pane": {"where-you-would-live": "Where you would live", "address": "Address", "neighborhood": "Neighborhood", "discover-neighborhood": "Discover neighborhood", "direction": "See on the map"}}, "property-navigation": {"previous-property": "Previous property", "next-property": "Next property"}, "property-openhouse": {"title": "Open house", "from": "from", "to": "to", "add-to-calendar": "Add to my calendar", "openhouse-at": "Openhouse at"}, "property-rooms": {"title": "Room descriptions", "level": "Level", "rooms": "Rooms", "floors": "Floors", "see-all-rooms": "See all rooms", "details": "Details"}, "property-tools": {"tab-label1": "Evaluation", "tab-label2": "Monthly payments", "tab-label3": "Potential gross revenue", "tool-expenses": {"property-price": "Property price", "loan-cost": "Loan amount", "downpayment": "Downpayment", "interest-rate": "Interest rate", "mortgage": "Amortization period", "frequency": "Payment frequency", "payments": "Payment", "chart-labels": {"municipal-tax": "Municipal tax", "energy": "Energy", "school-tax": "School tax"}, "mortgage-years": [{"value": 10, "name": "10 years"}, {"value": 15, "name": "15 years"}, {"value": 20, "name": "20 years"}, {"value": 25, "name": "25 years"}, {"value": 30, "name": "30 years"}], "frequency-paiements": [{"value": 12, "name": "Monthly"}, {"value": 24, "name": "Bimonthly"}, {"value": 26, "name": "Every two weeks"}, {"value": 52, "name": "Weekly"}]}, "tool-evaluation": {"year": "Year", "plot": "Plot", "building": "Building", "tax": "Welcome tax *", "total": "Sum total", "chart-labels": {"plot": "Plot", "building": "Building"}}, "tool-income": {"residential": "Residential", "commercial": "Commercial", "car-park": "Parking lots / garages", "other": "Other", "total": "Total", "chart-labels": {"residential": "Residential", "commercial": "Commercial", "car-park": "Parking lots / garages", "other": "Other"}}}, "statistics-evaluation": {"main-title": "Mortgage payments"}, "statistics-financy": {"main-title": "Financial details", "evaluation": {"title": "Evaluation", "field": "Plot evaluation", "building": "Building evaluation", "total": "Municipal evaluation"}, "taxes": {"title": "Taxes", "municipal": "Municipal tax", "school": "School tax", "total": "Total tax", "mutation": "Welcome tax", "mutation-text": "<strong>* Information to be used with caution and as a guideline only.</strong> The municipality may modify the rate or the method of calculation. Learn more about property transfer tax by <a href='https://www.oaciq.com/en/articles/property-transfer-duties' target='_blank'>clicking here</a> or contact your real estate agent."}, "income": {"title": "Gross annual revenue (Potential)", "residential": "Residential", "commercial": "Commercial", "car-park": "Parking lots / garages", "other": "Other", "total": "Total income"}, "other": {"title": "Other", "electricity": "Électricité"}}, "search-buy": {"title": "Find a property", "button": "Search", "placeholder": "City, neighborhood, street", "advanced-search": "Advanced search"}, "search-sell-small": {"subtitle": "Free assessment", "title": "Thinking of selling?", "description": "Curious about your property’s value? This process is completely commitment-free, and we’d be happy to connect and discuss it with you.", "input-placeholder": "Enter the address of your property", "button": "Get my free assessment"}, "search-full": {"alert": "For a custom search, we invite you to subscribe for free to our", "alert-link": "real estate alert", "filters": {"all": "All", "show": "Show", "results": "results", "reset-search": "Reset search", "real-estate-alert": "Real estate alert", "city-neighborhood": "City, neighborhood, street", "rooms": "Rooms", "more": "More", "more-mobile": "Filters", "property-search": "Search by city, neighborhood, address or Centris number", "property-search-mobile": "Search", "filters-toggle": "Filters", "filters-submit": "Show results", "filters-reset": "Reset", "clear-search": "Clear search", "revenues": "Income property", "property-categories": [{"id": "anyCategory", "label": "All categories"}, {"id": "residential", "label": "Residential"}, {"id": "commercial", "label": "Commercial"}], "property-types": [{"id": 0, "label": "For sell or rent"}, {"id": 1, "label": "For sell"}, {"id": 2, "label": "For rent"}], "groups": {"price": "Price", "rental-price": "Monthly cost", "property-type": "Property type", "plex": "Plex", "carac": "Characteristics", "crit": "Other criterias", "surface": "Surface", "no-filter": "Not available"}, "fields": {"bathroom": "Bathrooms", "bedroom": "Bedrooms", "garage": "Garage", "parking": "Parking", "openhouses": "Open houses", "reprise": "Repossession", "residential": "Residential", "commercial": "Commercial", "for-sale": "For sale", "for-rent": "For rent"}, "flags": [{"value": "openhouse", "label": "Open house"}, {"value": "property-groups", "label": "Property groups"}, {"value": "revenues", "label": "Income"}]}, "properties-pane": {"no-result": "We currently have no properties matching your criteria.", "no-result-alert": "Subscribe to our real estate alert and get notified as soon as a new property meeting your search criteria gets posted.", "real-estate-alert": "Real estate alert", "order": "Order"}, "sortable": {"title": "Properties for sale in Montreal", "newest": "Newest", "oldest": "Oldest", "higher-priced": "Higher priced", "lower-priced": "Lower priced", "settings": "Settings", "show-map": "Show map", "grid-only": "Grid only", "thumbnail-option": "Thumbnail option", "show": "Show", "results": "results", "result": "result", "reset-search": "Reset search", "house": "Properties", "map": "Map", "property-found": "property found", "properties-found": "properties found"}}, "search-sell": {"title": "How much is my property worth?", "description": "Receive a free assessment of your property's market value.", "input-placeholder": "Enter the address of your property", "button": "Evaluate"}, "search-simple": {"tab1": "Buy", "tab2": "<PERSON>ll", "tab3": "MLS", "advanced-search": "Advanced search", "search": "Search", "evaluate": "Evaluate", "city-or-address": "Please enter your address", "city-neighborhood": "City, neighborhood, street", "search-centris": "Enter a MLS Centris code"}, "sell-sheet": {"title": "Lorem ipsum...", "description": "Selling a property is so much more than just a transaction — it’s a life moment. With us, every client is supported as if they were part of the family. Backed by nearly 20 years of experience and a close-knit team, we provide guidance that is human, thorough, and always tailored to your needs. Our commitment goes far beyond the sale: we build lasting relationships based on trust, listening, and true availability. From the very first call to the notary’s office — and even beyond — you can count on a dedicated, skilled team that’s genuinely there for you every step of the way.", "button": "Contact us", "list": {"item1": "Item 1", "item2": "Item 2", "item3": "Item 3"}, "infobox": {"title": "You want to sell a property?", "description": "Contact us now to meet a real estate broker from our team.", "button": "Contact"}}, "specialists-card": {"website": "Website"}, "slider-content": {"title": "Sales strategy", "slides": [{"title": "Our team website", "text": "Our website is powered by the e-closion solution that uses excellent SEO practices. Therefore it ranks at the top of search engines, which gives you excellent visibility. In addition, it offers an optimal display of your property, not only on desktop but also on mobiles."}, {"title": "The strength of social networks", "text": "In addition to being posted on our website, your property is also published on our social media network. It provides greater visibility and our real estate brokers are able to answer any questions quickly."}, {"title": "HDR photographs of properties", "text": "High Dynamic Range Imaging (HDR) is achieved by capturing, then combining, various exposures of the same subject. These images are then merged to create a new image where every detail appears in its best light. The result is perfect!"}]}, "team-card-1": {"join": "Contact", "spoken": "Languages spoken: ", "alt-image": "Learn more about "}, "team-hero-1": {"title": "Our team", "description": "Nam quam ex, efficitur ac elit at, blandit interdum mi. In viverra nisl vitae est pulvinar lobortis. Duis elementum ex ac libero tincidunt, ut pretium nisi eleifend. Cras laoreet erat sit amet nisi sagittis, in condimentum tortor rhoncus. In vehicula elit mauris, vitae semper purus fermentum eget. Morbi tincidunt augue eros, eu posuere orci egestas in. Nullam at ex quis lorem cursus pellentesque sed eget nulla. Integer lobortis risus nec orci posuere accumsan. Curabitur sodales porta enim at condimentum.", "learn-more": "Learn more."}, "team-hero-2": {"title": "Our team", "description": "Nam quam ex, efficitur ac elit at, blandit interdum mi. In viverra nisl vitae est pulvinar lobortis. Duis elementum ex ac libero tincidunt, ut pretium nisi eleifend. Cras laoreet erat sit amet nisi sagittis, in condimentum tortor rhoncus. In vehicula elit mauris, vitae semper purus fermentum eget. Morbi tincidunt augue eros, eu posuere orci egestas in. Nullam at ex quis lorem cursus pellentesque sed eget nulla. Integer lobortis risus nec orci posuere accumsan. Curabitur sodales porta enim at condimentum.", "learn-more": "Learn more."}, "team-hero-3": {"title": "Our team", "description": "Nam quam ex, efficitur ac elit at, blandit interdum mi. In viverra nisl vitae est pulvinar lobortis. Duis elementum ex ac libero tincidunt, ut pretium nisi eleifend. Cras laoreet erat sit amet nisi sagittis, in condimentum tortor rhoncus. In vehicula elit mauris, vitae semper purus fermentum eget. Morbi tincidunt augue eros, eu posuere orci egestas in. Nullam. Curabitur sodales porta enim at condimentum."}, "team-hero-4": {"title": "Our team", "description": "Nam quam ex, efficitur ac elit at, blandit interdum mi. In viverra nisl vitae est pulvinar lobortis. Duis elementum ex ac libero tincidunt, ut pretium nisi eleifend. Cras laoreet erat sit amet nisi sagittis, in condimentum tortor rhoncus. In vehicula elit mauris, vitae semper purus fermentum eget. Morbi tincidunt augue eros, eu posuere orci egestas in. Nullam. Curabitur sodales porta enim at condimentum."}, "team-hero-5": {"broker-name": "<PERSON>", "broker-job": "Real estate broker", "broker-desc": "<PERSON> is a dynamic and dedicated real estate broker, with extensive experience in the real estate industry. After graduating with a degree in property management, <PERSON> quickly made her mark with her innovative approach and commitment to excellence. She is particularly recognized for her ability to understand the unique needs of her clients, which she always places at the center of her strategy. With in-depth knowledge of the real estate market, <PERSON> excels in negotiation, ensuring her clients get the best possible transactions. Her passion for real estate and attention to detail allows her to create personalized, memorable experiences for each client, whether they are first-time buyers or experienced sellers."}, "team-interstice-1": {"title": "Real estate brokers", "title-admin": "Administration", "infobox": {"title": "You want to buy or sell a property?", "description": "Contact us now to meet a real estate broker from our team.", "button": "Contact us"}}, "team-interstice-2": {"infobox": {"title": "You want to buy or sell a property?", "button": "Contact us"}}, "testimonials-slider": {"title": "Testimonials", "back": "Back"}, "toggler": {"list": "Properties", "map": "Map"}, "neighborhood-map": {"neighborhood-pane": {"placeholder": "Choose a neighbourhood", "title": "Our neighbourhoods", "button": "Contact our team", "description": "We have assembled here information about the neighbourhoods in which we work daily and we really like. Visit these neighbourhoods and find the one that best suits your needs!"}, "btn-discover": "Discover the neighbourhood"}, "neighborhood-interest": {"title": "Points of interest of"}, "neighborhood-demographics": {"title": "Demographic of", "area": "Area", "number": "Population", "density": "Density", "age": {"title": "Age groups (%)", "label14less": "14 and less", "label1524": "15-24 years", "label2544": "25-44 years", "label4564": "45-64 years", "label65more": "65 and more"}, "groups": {"title": "Households (%)", "label1": "1 members", "label2": "2 members", "label3": "3 members", "label4": "4 members", "label5": "5 members"}, "lang": {"title": "Spoken languages (%)", "labelfrench": "French-speaker", "labelenglish": "English-speaker", "labelother": "Allophone"}}, "neighborhood-avgcost": {"title": "Median cost in", "cost_unifamiliale": "Single-family", "cost_copropriete": "Condominium"}, "neighborhood-photo": {"title": "Photos of"}, "neighborhood-list": {"title": "The properties of"}, "neighborhood-grid": {"title": "Our sectors", "text": "Each neighbourhood has its own personality, atmosphere, and unique appeal. Some stand out for their proximity to schools and everyday conveniences, while others charm with their parks, peaceful streets, or river views. Because we live in these areas, know them inside and out, and have been exploring them for years, we understand every nuance — the properties, the iconic streets, and the hidden spots locals love.Our team is here to help you discover these living environments and guide you toward the one that best matches your lifestyle. Whether you're drawn to the energy of a lively neighbourhood, the calm of a residential setting, or something in between, there’s a place out there that feels just right for you.", "btn": "Learn more"}, "neighborhood-navigation": {"prevnav": "Previous neighborhood", "nextnav": "Next neighborhood"}, "neighborhoods-custom": {"subtitle": "Wider presence", "title": "We also serve the greater Québec City area and the South Shore", "text": "Beyond the neighbourhoods we work in every day, our team is also active throughout the greater Québec City area and the South Shore. No matter where your project is located, you can count on our thorough support, attention to detail, and genuine passion for real estate.", "cta-1": "Contact us", "cta-2": "See our properties"}}, "views": {"404": {"title": "404 error", "alt-image": "whoops something went wrong", "description": "This page does not exist or no longer exists. We apologize for the inconvenience.", "link-text": "We invite you to come back to", "link": "home page"}, "buy": {"blog-category": "buy", "title": "Looking to buy?"}, "favorites": {"title": "My favorites", "no-favorites": "No favorite", "button": "All our properties"}, "home": {"title": "Real estate is our passion. Since 2007", "btn1": "<PERSON>ll", "btn2": "Buy", "btn3": "Property search", "custom1": {"subtitle": "Unique opportunities", "title": "Find the neighbourhood that feels like home", "text": "Each area we serve has its own pace, personality, and that special something that makes it unique. Some offer unbeatable proximity to schools and services, others stand out with wide green spaces, stunning river views, or the peace of quiet residential streets. We know them inside and out — we’ve grown up there, we live there, or we’ve walked those streets for years. We understand the colours, the seasons, the hidden gems — all the little things that shape everyday life and make you want to stay. Let our team help you discover the one that truly fits who you are. Whether you're dreaming of calm, vibrancy, or a bit of both, there's a corner of the city that’s just right for you.", "btn": "Discover our sectors"}, "custom2": {"subtitle": "Real estate investment", "title": "Explore the options", "text": "<strong>There are many ways to invest in real estate — each with its own set of opportunities.</strong> Whether you’re looking to diversify your portfolio, generate passive income, or plan a long-term project, this section gives you a clear and concise overview of the most promising avenues. Income properties, short-term rentals, high-potential land, or real estate abroad: explore investment options tailored to your goals, your budget, and your vision as an investor.", "btn": "Explore the options"}}, "homestaging": {"title": "Home staging projects", "cta": "Would you like to get a free consultation?", "button": "Contact us"}, "landing": {"btn-visit": "Visit our website", "form-title": "Laissez-nous vos coordonnées, un courtier vous rappelera en moins de 24 heure!", "share": "Share"}, "neighborhood": {"cta": "Back to neighborhoods"}, "openhouse": {"title": "Open house", "description": "Do you want to visit a property without making an appointment? Select a time range to find a property that fits your needs.", "placeholder": "Date", "no-result": "There are no open house visits scheduled at the moment.", "all-our-properties": "All our properties", "today": "Today"}, "propertygroups": {"title": "Investment", "description": "Whether you’re a seasoned investor or considering your first project, this section is designed to showcase promising investment opportunities: income properties, short-term rentals, high-potential land, and international real estate.", "cta": "Contact us"}, "sell": {"blog-category": "sell", "title": "Looking to sell?"}, "specialists": {"title": "Our specialists", "all": "All"}, "testimonials": {"title": "Testimonials", "video-filter": "Video testimonials"}, "privacy-policy": {"title": "Privacy policy", "content": "<h3>Privacy policy</h3>"}}}