.loading {
	opacity: 0 !important;
}

.show-mobile {
	display: none;

	@media (max-width: 992px) {
		display: block;
	}
}

/* List */
.page-description, .-page-description{
	ul{
	  padding-left: 0px;
	  padding-bottom: 30px;
	  list-style: none;
  
	  li {
		position: relative;
		padding: 24px 0px 24px 40px;
		font-family: var(--font-special);
		border-bottom: 1px solid rgba($color: $black, $alpha: 0.2);
  
		&:before {
		  color: var(--primary-color);
		  font-family: 'icomoon';
		  speak: none;
		  font-style: normal;
		  font-weight: normal;
		  font-variant: normal;
		  text-transform: none;
		  line-height: 1;
		  -webkit-font-smoothing: antialiased;
		  -moz-osx-font-smoothing: grayscale;
		  content: "\e90d";
		  position: absolute;
		  left: 0;
		  top: 28px;
		}
	  }
	}
  }
  .list {
	  padding-left: 0px;
	  padding-bottom: 30px;
  
	  li {
		  position: relative;
		  padding: 16px 0px 16px 40px;
		  font-family: var(--font-special);
		  border-bottom: 1px solid rgba($color: $black, $alpha: 0.2);
  
		  &:before {
			  color: var(--primary-color);
			  font-family: 'icomoon';
			  speak: none;
			  font-style: normal;
			  font-weight: normal;
			  font-variant: normal;
			  text-transform: none;
			  line-height: 1;
			  -webkit-font-smoothing: antialiased;
			  -moz-osx-font-smoothing: grayscale;
			  content: "\e90d";
			  position: absolute;
			  left: 0;
			  top: 22px;
		  }
	  }
  }
  
  
  
/* Sortable List */
.sortable-list {
	gap: 0 !important;
	margin: 30px 0;
	margin-bottom: 80px;

	.input-tab {
		display: none;
	}

	input:checked + label {
		color: var(--secondary-color);

		&:after {
			width: 100%;
		}
	}

	input:hover + label {
		color: var(--secondary-color);

		&:after {
			width: 100%;
		}
	}

	label {
		@extend .-cta-medium;
		margin-right: 24px;
		position: relative;
		padding: 0 0px 7px;
		color: var(--color-text);
		text-align: center;
		overflow: inherit;
		display: inline-block;
		line-height: initial;

		&:hover{
			cursor: pointer;
		}

		@media (max-width: 1050px) {
			@include fontSize(13px);
			margin-right: 15px;
		}

		&:last-child {
			margin-right: 0;
		}

		&:after {
			position: absolute;
			bottom: 0;
			left: 0;
			content: "";
			background: var(--secondary-color);
			height: 2px;
			display: block;
			width: 0px;
			transition: all 0.5s cubic-bezier(0.19, 1, 0.22, 1);
		}
	}
}

// /*
// 	Loading
// */

.lds-ripple {
	display: inline-block;
	position: relative;
	width: 64px;
	height: 64px;
}

.lds-ripple div {
	position: absolute;
	border: 4px solid rgba(0, 0, 0, 0.35);
	opacity: 1;
	border-radius: 50%;
  	transform-origin: center;
	animation: lds-ripple 1.3s cubic-bezier(0, 0.2, 0.8, 1) infinite;
}

.lds-ripple div:nth-child(2) {
	animation-delay: -0.5s;
}

@keyframes lds-ripple {
	0% {
		top: 28px;
		left: 28px;
		width: 0;
		height: 0;
		opacity: 1;
	}
	100% {
		top: -1px;
		left: -1px;
		width: 58px;
		height: 58px;
		opacity: 0;
	}
}


// /*
// 	Loading circle
// */

$base-line-height: 50px;
$color: var(--primary-color);
$off: var(--primary-color);
$spin-duration: 1s;
$pulse-duration: 750ms;

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-circle {
  border-radius: 50%;
  width: $base-line-height;
  height: $base-line-height;
  border: .25rem solid $off;
  border-top-color: $color;
  animation: spin $spin-duration infinite linear;
}

// Hide duplicated swiper icon
.swiper-button-next::after,
.swiper-button-prev::after {
  content: none;
}

// Steps
.step {
  position: absolute;
  top: 0;
  right: 0;
  border-radius: 20px;
  border: 2px solid #999;
  width: 78px;
  height: 38px;
  display: flex;
  align-items: center;
  justify-content: center;

  p {
    margin: 0;
    text-align: center;
	@extend .-cta-small;
	font-weight: 600;
  }
}

// .clear{
// 	clear: both;
// }

// .row {
// 	margin-bottom: 0;
// }

// .swiper-button-disabled {
// 	opacity: 0 !important;
// }

// .ap-input {
// 	background: transparent !important;
// 	border: none !important;
// }

// .container {
// 	position: relative;
// 	display: block;

// 	&.-small {
// 		max-width: 980px;
// 	}

// 	&.-smaller {
// 		max-width: 780px;
// 	}
// }

// .title.with-icon {
// 	display: flex;
// 	align-items: center;
// }

// .-center{
// 	text-align: center;
// }

// .-hide {
// 	display: none !important;
// }

// .hide-mobile {
// 	@media (max-width: 992px) {
// 		display: none;
// 	}
// }

// // Neighborhood / propertygroup section titles
// .single-title {
// 	@include fontSize(32px, 39px);
// 	text-align: center;
// 	color: black;
// 	margin-top: 50px;
// 	margin-bottom: 40px;
// }

// /* Tabs Ctn */
// .tabs-ctn {
// 	.input-tab {
// 		display: none;
// 	}
// }

// .tabs {
// 	padding-left: 0;
// }

// /*
// 	Page Head
// */

// .-page-head {
// 	padding-top: 60px;
// 	text-align: center;

// 	.page-title {
// 		margin-top: 0;
// 		margin-bottom: 30px;
// 	}

// 	.page-description {
// 		margin: 0 auto;
// 		max-width: 510px;
// 		color: #777777;
// 		font-family: var(--font-special);
// 		@include fontSize(var(--font-size-small), 28px);
// 		text-align: center;
// 	}
// }

// /*
// 	Share Social Cpn
// */

// .share-social-cpn {
// 	display: flex;
// 	align-items: center;

// 	.share-btn{
// 		background-color:transparent !important;
// 		border:none !important;
// 		color: #666666;
// 		margin-right:40px;
// 		padding:0;
// 		margin-top:-7px;
// 		cursor: pointer;

// 		 &:hover{
// 			opacity: 0.6;
// 		 }

// 		&:last-child{
// 			margin-right:0;
// 		}
// 	}

// 	.sb-button {
// 		margin: 0;
// 		cursor: pointer;
// 	}

// 	.share-print {
// 		margin-right: 40px;
// 		&:hover{
// 			opacity: 0.6;
// 		 }
// 	}

// 	.sb-wrapper {
// 		min-width: 0;
// 		height: auto;
// 		border: 0;
// 		background: none;

// 		.sb-icon {
// 			min-width: 0;
// 		}
// 	}

// 	.sb-group {
// 		display: flex;

// 		.sb-wrapper {
// 			margin-right: 40px;
// 		}

// 		.sb-pinterest {
// 			margin-right: 0;
// 		}
// 	}
// }


// /*
// 	Sb share
// */
// .sb-wrapper {
// 	.sb-inner {
//     	display: none !important;
//     }
// }

// .sb-email, .sb-facebook, .sb-twitter {
// 	font-family: 'icomoon';
//     speak: none;
//     font-style: normal;
//     font-weight: normal;
//     font-variant: normal;
//     text-transform: none;
//     line-height: 1;
//     -webkit-font-smoothing: antialiased;
//     font-size: 15px !important;
//     color: #666666;

//     &:hover {
//     	color: #999999;
//     }
// }

// .sb-email {
// 	&:before {
// 		content: "\e904";
// 	}
// }

// .sb-facebook {
// 	&:before {
// 		content: "\e900";
// 	}
// }

// .sb-twitter {
// 	margin-right: 0 !important;

// 	&:before {
// 		content: "\e952";
// 	}
// }

// // move away
// .icon-alerte {
// 	font-size: $alertButtonIconSize;
// 	vertical-align: middle;
// 	margin-right: 10px;
// }

// // Fix swiper overflow
// .swiper-container { overflow: hidden; }

// // Fix Googlemaps auto-complete field properties
// .pac-container.pac-logo {
//   border: 0 none;
//   box-shadow: none;
//   &::after { display: none !important; }
// }
