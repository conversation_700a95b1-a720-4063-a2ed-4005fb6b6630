body {
    font-family: var(--font-primary);
    font-size: var(--font-size-medium);
    color: var(--color-text);
    background-color: var(--color-bg-white);
    line-height: var(--line-height-medium);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;

    &.-no-scroll{
        margin: 0;
        height: 100%;
        overflow: hidden;
    }
}

.container{
    @include container;

    &.-narrow, .container-narrow {
        margin-left: grid-space(math.div(2,12), 1);
        margin-right: grid-space(math.div(2,12), 1);

        @media (max-width: $tablet-lg) {
            margin-left: unset;
            margin-right: unset;
        }
    }

    &.-small, .container-small {
        margin-left: grid-space(math.div(1,12), 1);
        margin-right: grid-space(math.div(1,12), 1);

        @media (max-width: $tablet-lg) {
            margin-left: unset;
            margin-right: unset;
        }
    }

    &.-wide, .container-wide {
        --container-margin: 20px;
    }
}

.main-content{
    background-color: var(--color-bg);
    min-height: vh(100);
}