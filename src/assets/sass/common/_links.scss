/*
	Buttons
*/
$defaultButtonHeight: 60px;

$primaryButtonBG: var(--primary-color);
$primaryButtonBGHover: $red;
$primaryButtonTextColor: $white;
$primaryButtonTextColorHover: $white;
$primaryButtonSize: var(--font-size-cta-medium);
$primaryButtonLineHeight: 17px;
$primaryButtonWeight: 900;
$primaryButtonAlign: center;
// $primaryButtonFont: $primaryFont;
$primaryButtonPadding: 17px 52px;
$primaryButtonBorder: none;
$primaryButtonBorderRadius: 100px;
$primaryButtonTextTransform: initial;
$primaryButtonMinWidth: 250px;
$primaryButtonHeight: $defaultButtonHeight;

$secondaryButtonBG: transparent;
$secondaryButtonBGHover: var(--primary-color);
$secondaryButtonTextColor: var(--color-text);
$secondaryButtonTextColorHover: $white;
$secondaryButtonSize: var(--font-size-cta-medium);
$secondaryButtonLineHeight: normal;
$secondaryButtonWeight: 900;
$secondaryButtonAlign: center;
// $secondaryButtonFont: $primaryFont;
$secondaryButtonPadding: 17px 52px;
$secondaryButtonBorder: 2px solid $black;
$secondaryButtonBorderRadius: 100px;
$secondaryButtonTextTransform: initial;
$secondaryButtonMinWidth: 250px;
$secondaryButtonHeight: $defaultButtonHeight;

$ghostButtonBG: var(--primary-color);
$ghostButtonBGHover: $red;
$ghostButtonTextColor: $white;
$ghostButtonTextColorHover: var(--primary-color);
$ghostButtonSize: var(--font-size-cta-medium);
$ghostButtonLineHeight: normal;
$ghostButtonWeight: 600;
$ghostButtonAlign: center;
// $ghostButtonFont: $primaryFont;
$ghostButtonPadding: 20px 32px;
$ghostButtonBorder: 2px solid var(--primary-color);
$ghostButtonBorderHover: 2px solid $red;
$ghostButtonBorderRadius: 100px;
$ghostButtonTextTransform: uppercase;
$ghostButtonMinWidth: fit-content;
$ghostButtonHeight: $defaultButtonHeight;

$whiteButtonBG: $white;
$whiteButtonBGHover: transparent;
$whiteButtonTextColor: var(--primary-color);
$whiteButtonTextColorHover: $white;
$whiteButtonSize: var(--font-size-cta-medium);
$whiteButtonLineHeight: var(--line-height-cta-medium);
$whiteButtonWeight: 900;
$whiteButtonAlign: center;
// $whiteButtonFont: $primaryFont;
$whiteButtonPadding: 10px 32px;
$whiteButtonBorder: 2px solid white;
$whiteButtonBorderHover: 2px solid white;
$whiteButtonBorderRadius: 100px;
$whiteButtonTextTransform: initial;
$whiteButtonMinWidth: fit-content;
$whiteButtonHeight: $defaultButtonHeight;

$primarySmallButtonBG: var(--primary-color);
$primarySmallButtonBGHover: $red;
$primarySmallButtonTextColor: $white;
$primarySmallButtonTextColorHover: $white;
$primarySmallButtonSize: 15px;
$primarySmallButtonLineHeight: 17px;
$primarySmallButtonWeight: 600;
$primarySmallButtonAlign: center;
// $primarySmallButtonFont: $primaryFont;
$primarySmallButtonPadding: 7px 20px;
$primarySmallButtonBorder: none;
$primarySmallButtonBorderRadius: 100px;
$primarySmallButtonTextTransform: none;
$primarySmallButtonMinWidth: 140px;
$primarySmallButtonHeight: 35px;

$secondarySmallButtonBG: #AAAAAA;
$secondarySmallButtonBGHover: var(--primary-color);
$secondarySmallButtonTextColor: $white;
$secondarySmallButtonTextColorHover: $white;
$secondarySmallButtonSize: 13px;
$secondarySmallButtonLineHeight: 17px;
$secondarySmallButtonWeight: 900;
$secondarySmallButtonAlign: center;
// $secondarySmallButtonFont: $primaryFont;
$secondarySmallButtonPadding: 7px 20px;
$secondarySmallButtonBorder: none;
$secondarySmallButtonBorderRadius: 9px;
$secondarySmallButtonTextTransform: none;
$secondarySmallButtonMinWidth: 140px;
$secondarySmallButtonHeight: auto;

$alertButtonBG: #AAAAAA;
$alertButtonBGHover: var(--primary-color);
$alertButtonTextColor: $white;
$alertButtonTextColorHover: $white;
$alertButtonSize: 13px;
$alertButtonIconSize: 22px;
$alertButtonWeight: 900;
$alertButtonAlign: center;
// $alertButtonFont: $primaryFont;
$alertButtonPadding: 10px 20px;
$alertButtonBorder: none;
$alertButtonBorderRadius: 30px;
$alertButtonTextTransform: none;
$alertButtonMinWidth: 140px;
$alertButtonHeight: $defaultButtonHeight;

$favoriteButtonBG: transparent;
$favoriteButtonBGHover: var(--primary-color);
$favoriteButtonTextColor: #000000;
$favoriteButtonTextColorHover: $white;
$favoriteButtonSize: var(--font-size-cta-small);
$favoriteButtonLineHeight: 17px;
$favoriteButtonWeight: 900;
$favoriteButtonAlign: center;
// $favoriteButtonFont: $primaryFont;
$favoriteButtonPadding: 17px 52px;
$favoriteButtonBorder: 1px solid #DDDDDD;
$favoriteButtonBorderRadius: 100px;
$favoriteButtonTextTransform: none;
$favoriteButtonMinWidth: 280px;
$favoriteButtonHeight: $defaultButtonHeight;

$phoneButtonBG: transparent;
$phoneButtonBGHover: var(--primary-color);
$phoneButtonTextColor: var(--color-text);
$phoneButtonTextColorHover: $white;
$phoneButtonSize: var(--font-size-medium);
$phoneButtonLineHeight: 32px;
$phoneButtonWeight: 600;
$phoneButtonAlign: center;
// $phoneButtonFont: $primaryFont;
$phoneButtonPadding: 9px 26px;
$phoneButtonBorder: 1px solid #979797;
$phoneButtonBorderRadius: 50px;
$phoneButtonTextTransform: none;
$phoneButtonMinWidth: 140px;
$phoneButtonHeight: $defaultButtonHeight;

$smallLinkColor: var(--primary-color);
// $smallLinkFont: $primaryFont;
$smallLinkSize: var(--font-size-cta-small);
$smallLinkWeight: 900;
$smallLinkLineHeight: 17px;
$smallLinkTextTransform: uppercase;

.btn-ctn {
    // 
}

.main-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: max-content;
  padding: 0 50px;

	&:hover {
		cursor: pointer;
	}

	&:disabled {
		cursor: default;
		pointer-events: none;
		opacity: 0.5;
	}

	&.-primary {
		min-width: $primaryButtonMinWidth;
        height: $primaryButtonHeight;
		padding: $primaryButtonPadding;
		background: $primaryButtonBG;
		color: $primaryButtonTextColor;
		// font-family: $primaryButtonFont;
		font-size: $primaryButtonSize;
		font-weight: $primaryButtonWeight;
		text-align: $primaryButtonAlign;
		text-transform: $primaryButtonTextTransform;
		line-height: $primaryButtonLineHeight;
		border: $primaryButtonBorder;
		border-radius: $primaryButtonBorderRadius;
		transition: $primaryAnimation;

		&:hover {
			background: $primaryButtonBGHover;
			color: $primaryButtonTextColorHover;
		}
	}

	&.-secondary {
		min-width: $secondaryButtonMinWidth;
        height: $secondaryButtonHeight;
		padding: $secondaryButtonPadding;
		background: $secondaryButtonBG;
		color: $secondaryButtonTextColor;
		// font-family: $secondaryButtonFont;
		font-size: $secondaryButtonSize;
		font-weight: $secondaryButtonWeight;
		text-align: $secondaryButtonAlign;
		text-transform: $secondaryButtonTextTransform;
		line-height: $secondaryButtonLineHeight;
		border: $secondaryButtonBorder;
		border-radius: $secondaryButtonBorderRadius;
		transition: $primaryAnimation;

		&:hover {
			background: $secondaryButtonBGHover;
			color: $secondaryButtonTextColorHover;
		}
	}

	&.-ghost {
		min-width: $ghostButtonMinWidth;
        height: $ghostButtonHeight;
		padding: $ghostButtonPadding;
		background: $ghostButtonBG;
		color: $ghostButtonTextColor;
		// font-family: $ghostButtonFont;
		font-size: $ghostButtonSize;
		font-weight: $ghostButtonWeight;
		text-align: $ghostButtonAlign;
		text-transform: $ghostButtonTextTransform;
		line-height: $ghostButtonLineHeight;
		border: $ghostButtonBorder;
		border-radius: $ghostButtonBorderRadius;
		transition: $primaryAnimation;

		&:hover {
			background: $ghostButtonBGHover;
			color: $ghostButtonTextColorHover;
			border: $ghostButtonBorderHover;
		}
	}

	&.-white {
		min-width: $whiteButtonMinWidth;
        height: $whiteButtonHeight;
		padding: $whiteButtonPadding;
		background: $whiteButtonBG;
		color: $whiteButtonTextColor;
		// font-family: $whiteButtonFont;
		font-size: $whiteButtonSize;
		font-weight: $whiteButtonWeight;
		text-align: $whiteButtonAlign;
		text-transform: $whiteButtonTextTransform;
		line-height: $whiteButtonLineHeight;
		border: $whiteButtonBorder;
		border-radius: $whiteButtonBorderRadius;
		transition: $primaryAnimation;

		&:hover {
			background: $whiteButtonBGHover;
			color: $whiteButtonTextColorHover;
			border: $whiteButtonBorderHover;
		}

    @media (max-width:450px) {
      min-width: initial;
    }
	}

	&.-alert {
		min-width: $alertButtonMinWidth;
        height: $alertButtonHeight;
		padding: $alertButtonPadding;
		background: $alertButtonBG;
		color: $alertButtonTextColor;
		// font-family: $alertButtonFont;
		font-size: $alertButtonSize;
		font-weight: $alertButtonWeight;
		text-align: $alertButtonAlign;
		text-transform: $alertButtonTextTransform;
		border: $alertButtonBorder;
		border-radius: $alertButtonBorderRadius;
		transition: $primaryAnimation;

		&:hover {
			background: $alertButtonBGHover;
			color: $alertButtonTextColorHover;
		}
	}

	&.-favorite {
		min-width: $favoriteButtonMinWidth;
        height: $favoriteButtonHeight;
		padding: $favoriteButtonPadding;
		background: $favoriteButtonBG;
		color: $favoriteButtonTextColor;
		// font-family: $favoriteButtonFont;
		font-size: $favoriteButtonSize;
		font-weight: $favoriteButtonWeight;
		text-align: $favoriteButtonAlign;
		text-transform: $favoriteButtonTextTransform;
		line-height: $favoriteButtonLineHeight;
		border: $favoriteButtonBorder;
		border-radius: $favoriteButtonBorderRadius;
		transition: $primaryAnimation;

		&:hover {
			background: $favoriteButtonBGHover;
			color: $favoriteButtonTextColorHover;
		}
	}

	&.-phone {
		min-width: $phoneButtonMinWidth;
        height: $phoneButtonHeight;
		padding: $phoneButtonPadding;
		background: $phoneButtonBG;
		color: $phoneButtonTextColor;
		// font-family: $phoneButtonFont;
		font-size: $phoneButtonSize;
		font-weight: $phoneButtonWeight;
		text-align: $phoneButtonAlign;
		text-transform: $phoneButtonTextTransform;
		line-height: $phoneButtonLineHeight;
		border: $phoneButtonBorder;
		border-radius: $phoneButtonBorderRadius;
		transition: $primaryAnimation;

		&:hover {
			background: $phoneButtonBGHover;
			color: $phoneButtonTextColorHover;
		}

        i.icon-mobile {
            margin-right: 10px;
        }
	}

    &.-previous {
        padding: 0px;
        margin-right: 40px;
		@extend .-cta-medium;
        cursor: pointer;
    }

	&.-primary-small {
		min-width: $primarySmallButtonMinWidth;
        height: $primarySmallButtonHeight;
		padding: $primarySmallButtonPadding;
		background: $primarySmallButtonBG;
		color: $primarySmallButtonTextColor;
		// font-family: $primarySmallButtonFont;
		font-size: $primarySmallButtonSize;
		font-weight: $primarySmallButtonWeight;
		text-align: $primarySmallButtonAlign;
		text-transform: $primarySmallButtonTextTransform;
		line-height: $primarySmallButtonLineHeight;
		border: $primarySmallButtonBorder;
		border-radius: $primarySmallButtonBorderRadius;
		transition: $primaryAnimation;

		&:hover {
			background: $primarySmallButtonBGHover;
			color: $primarySmallButtonTextColorHover;
		}
	}

	&.-secondary-small {
		min-width: $secondarySmallButtonMinWidth;
        height: $secondarySmallButtonHeight;
		padding: $secondarySmallButtonPadding;
		background: $secondarySmallButtonBG;
		color: $secondarySmallButtonTextColor;
		// font-family: $secondarySmallButtonFont;
		font-size: $secondarySmallButtonSize;
		font-weight: $secondarySmallButtonWeight;
		text-align: $secondarySmallButtonAlign;
		text-transform: $secondarySmallButtonTextTransform;
		line-height: $secondarySmallButtonLineHeight;
		border: $secondarySmallButtonBorder;
		border-radius: $secondarySmallButtonBorderRadius;
		transition: $primaryAnimation;

		&:hover {
			background: $secondarySmallButtonBGHover;
			color: $secondarySmallButtonTextColorHover;
		}
	}

  // Prevent button from breaking viewport
  @media (max-width: 450px) {
    &:not(.-primary-small, .-secondary-small) {
      min-width: initial !important;
      max-width: 100% !important;
      height: auto !important;
    }
  }
}

.small-link {
    position: relative;
    color: $smallLinkColor;
    // font-family: $smallLinkFont;
	@extend .-cta-medium;
	display: flex;
	align-items: center;

    &.-no-uppercase {
        text-transform: none;
    }

    &.right:hover i {
        right: -5px;
    }

    i {
        position: relative;
        right: 0;
        transition: $primaryAnimation;

        &.icon-map-plus {
            font-size: 10px;
            margin-right: 7px;
        }

        &.icon-arrow-right {
            padding-left: 5px;
        }
    }
}


.centered-cta-cpn {
	.centered-cta {
		display: flex;
		justify-content: center;
		width: auto;
		margin: 20px auto 100px;

		a {
			display: flex;
			align-items: center;
			i {
				font-size: 15px;
				padding-right: 10px;
			}
		}
	}
}



// this is from the update

a,
button,
input[type="submit"] {
  position: relative;
  display: inline-block;
  color: var(--primary-color);
  outline: none;
  border: 0 none;
  background: none;
  text-decoration: none;
  transition: $transition;

  &::before, &::after { transition: $transition; }
}



