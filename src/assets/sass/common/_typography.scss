/**
  * This file contains code related to typography - titles, paragraphs, text modifiers, etc
  */


// // // // //
// HEADINGS // 
// // // // //

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-title);
  color: var(--color-title); 
  margin: 40px 0;

  // Clickable titles
  a {
    color: var(--color-title);
  }
}

h1 {
  font-size: var(--font-size-h1);
  line-height: var(--line-height-h1);
  font-weight: 300;
  letter-spacing: -0.42px;

  // &.-large, &.-display {
  //   font-size: var(--font-size-h1-large);
  //   line-height: var(--line-height-h1-large);
  // }

  &.-small {
    font-size: var(--font-size-h1-small);
    line-height: var(--line-height-h1-small);
    font-weight: 400;
    letter-spacing: normal;
  }

  &.-display {
    font-size: var(--font-size-h1-display);
    line-height: var(--line-height-h1-display);
    font-weight: 400;
    letter-spacing: normal;
  }
}

h2 {
  font-size: var(--font-size-h2);
  line-height: var(--line-height-h2);
  letter-spacing: -0.53px;
  font-weight: normal;

  @media (max-width: $desktop-lg) {
    letter-spacing: -0.36px;
  }

  &.-large {
    font-size: var(--font-size-h2-large);
    line-height: var(--line-height-h2-large);
    letter-spacing: normal;
  }

  &.-small {
    font-size: var(--font-size-h2-small);
    line-height: var(--line-height-h2-small);
    letter-spacing: normal;

    @media (max-width: $desktop-lg) {
      letter-spacing: -0.32px;
    }
  }
}

h3 {
  font-size: var(--font-size-h3);
  line-height: var(--line-height-h3);
  letter-spacing: -1px;
  font-weight: 400;

  // &.-small {
  //   font-size: var(--font-size-h3-small);
  //   line-height: var(--line-height-h3-small);
  // }
}

h4 {
  font-size: var(--font-size-h4);
  line-height: var(--line-height-h4);
  font-weight: normal;
}

h5 {
  font-size: var(--font-size-h5);
  line-height: var(--line-height-h5);
}

h6 {
  font-size: var(--font-size-h6);
  line-height: var(--line-height-h6);
}

.-h1 { @extend h1; }
.-h2 { @extend h2; }
.-h3 { @extend h3; }
.-h4 { @extend h4; }
.-h5 { @extend h5; }
.-h6 { @extend h6; }



// // // // // //
//  PARAGRAPHS // 
// // // // // //

p{
  font-family: var(--font-primary);
  font-size: var(--font-size-medium);
  line-height: var(--line-height-medium);
  margin: 1rem 0;
}

.--large {
  font-size: var(--font-size-large);
  line-height: var(--line-height-large);
}

.--normal {
  font-size: var(---font-size-medium);
  line-height: var(--line-height-medium);
}

.--small {
  font-size: var(--font-size-small);
  line-height: var(--line-height-small);
}

.--xsmall {
  font-size: var(--font-size-xsmall);
  line-height: normal;

  @media (max-width: $desktop-lg) {
    line-height: var(--line-height-xsmall);
  }
}

.--description {
  font-size: var(--font-size-description);
  line-height: var(--line-height-description);
}

.--testimony {
  font-size: var(--font-size-testimony);
  line-height: var(--line-height-testimony);
}



// // // // //
// SPECIALS // 
// // // // //

// Prices
.-prices-large {
  font-size: var(--font-size-prices-large);
  line-height: var(--line-height-prices-large);
}
.-prices-small {
  font-size: var(--font-size-prices-small);
  line-height: var(--line-height-prices-small);
}

// CTA
.-cta-large {
  font-size: var(--font-size-cta-large);
  line-height: var(--line-height-cta-large);
}
.-cta-medium {
  font-size: 18px;
  line-height: var(--line-height-cta-medium);
  font-weight: bold;
}
.-cta-small {
  font-size: var(--font-size-cta-small);
  line-height: var(--line-height-cta-small);
}

// Surtitles
.-eyebrow-large {
  font-size: var(--font-size-eyebrow-large);
  line-height: var(--line-height-eyebrow-large);
  font-weight: 600;
  letter-spacing: 3px;
  text-transform: uppercase;
}
.-eyebrow {
  font-size: var(--font-size-eyebrow);
  line-height: var(--line-height-eyebrow);
  font-weight: 600;
  letter-spacing: 6px;
  text-transform: uppercase;
}

// Thumbnails
.-thumbnail-title {
  font-size: var(--font-size-thumbnail-title);
  line-height: var(--line-height-thumbnail-title);
}
.-thumbnail-meta {
  font-size: var(--font-size-thumbnail-meta);
  line-height: var(--line-height-thumbnail-meta);
  font-weight: 600;
  letter-spacing: 2px;
  text-transform: uppercase;
  color: var(--secondary-color);
}
.-thumbnail-description {
  font-size: var(--font-size-thumbnail-description);
  line-height: var(--line-height-thumbnail-description);
}
