/*
	Alert Form Components
*/

.alert-form-cpn {
	padding: 80px 0;
	background: url('/assets/images/turmel/backgrounds/bg_alerte.webp') no-repeat center center;
	background-size: cover;

	.-emphase-title {
		margin-bottom: 20px;
	}

	.container {

		&.send{
			display: flex;
			justify-content: center;
			align-items: center;

			.alert-form{
				opacity: 0;
			}
		}

		@media (max-width:767px) {

			&.send {
				.alert-form{
					display: none;
				}
			}
		}
	}

	.alert-form {
		background: $white;
		border-radius: 24px;
		padding: 80px;
		box-shadow: 0px 4px 45px 0px rgba(0, 0, 0, 0.10);

		@media (max-width: 767px) {
			padding: 40px 20px;
		}

		#step3 {

			.form-group.-flex {
				display: flex;
			}
		}
	}

	.loading-inner{
		opacity: 0.5;
	}

	.form-loader{
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%,-50%);
	}

	.form-head {
		position: relative;
		padding-bottom: 40px;
		display: flex;
		align-items: center;
		justify-content: space-between;

		@media (max-width: 767px) {
			flex-direction: column;
			align-items: flex-start;
			gap: 20px;
			padding-bottom: 0;
		}

		h1 {
			@media (max-width: 767px) {
				order: 2;
			}
		}

		.step {
			position: unset;

			@media (max-width: 767px) {
				order: 1;
			}
		}
	}

	.-page-description {
		margin-top: 20px;
		margin-bottom: 0;
		padding-bottom: 40px;
		color: var(--color-text);
		font-family: var(--font-secondary);
	}

	.location-ctn, .type-ctn {
		padding-top: 20px;
		@include clearfix;

		.custom-checkbox-group {
			width: 50%;
			float: left;
			margin-bottom: 16px;

			@media (max-width: 767px) {
				float: none;
				width: 100%;
			}

			label {
				color: $black;
			}
		}
	}

	.location-ctn {

	  .separator {
	    border-bottom: 1px solid #e1e1e1;
	    margin-bottom: 15px;
	    padding-top: 15px;
	    clear: both;

	    p {
	      text-transform: uppercase;
	      color: $black;
	      font-weight: 700;
	      font-size: 15px;
	      line-height: 16px;
	    }
	  }
	}

	.input-ctn {
		padding-top: 40px;
	}

	.form-row {
		margin: 0;
	}

	.warning-message {
		margin-top: 35px;
		margin-bottom: 0;
		@extend .--small;
		text-align: right;
		width: 100%;
	}

	.-page-required{
		color: var(--color-text);
		font-family: var(--font-secondary);
		font-size: 12px;
		line-height: var(--line-height-cta-small);
		text-align: right;
	}

	.button-ctn {
		display: flex;
		align-items: center;
		justify-content: flex-end;
		padding-top: 40px;

		@media(max-width: 500px){
			flex-direction: column;
			align-items: flex-start;

			.-previous{
				margin-bottom:20px;
			}

		}
	}

	.form-response {
		position: absolute;
		text-align: center;
		opacity: 0;
		.button-ctn {
			justify-content: center;
		}

		&.show{
			opacity: 1;
			transition: all 0.5s ease-in-out;
		}

		@media(max-width: 767px){
			padding: 30px;

			&.show{
				position: relative;
			}
		}
	}
}
