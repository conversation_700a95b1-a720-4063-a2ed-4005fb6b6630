/*
	Blog List Component
*/
$blogListPadding: 50px 0px 60px 0;
$blogListBackground: var(--color-bg);

$blogListDateColor: $light-grey;
$blogListDateSize: 13px;
$blogListDateLineHeight: 24px;

$blogListDescriptionColor: var(--color-text);
$blogListDescriptionSize: 14px;
$blogListDescriptionLineHeight: 24px;

.blog-list-cpn {
	background-color: $blogListBackground;

	&.-bg-white {
		background-color: $white;
	}

	&.-no-border {

		.article {
			border-bottom: none;
		}
	}

	h2 {
		margin-bottom: 30px;
	}

	&.blog-list-2, &.blog-list-3 {
		.article-list-ctn {
			.article {
				.title {
					font-family: var(--font-secondary);
				}
			}
		}
	}

	.small-link {
		padding-right: 0px;
	}

	.cpn-head {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.article-list-ctn {
		padding-top: 20px;
		gap: 40px;

		@media (max-width: 767px) {
			gap: 0;
		}

		&.-full {
			.article {
				width: 100%;
				margin-right: 0;
			}

			img {
				@include img();
			}
		}
	}

	.article {
		position: relative;
		flex-grow: 1;
		//border-top: 1px solid $light-grey;
		//padding-top: 64px;
		border-bottom: 1px solid $light-grey;
		margin-bottom: 64px;
		padding-bottom: 64px;
		cursor: pointer;

		&:last-child {
			border-bottom: none;
			margin-bottom: 0;
			padding-bottom: 0;
		}

		.grid {
			gap: 80px;

			@media (max-width: 992px) {
				gap: 0;
			}
		}

		&:only-of-type{
			flex-grow: 0;
		}

		&:nth-child(3n), &:last-child{
			margin-right: 0;
		}

		&:nth-child(n+4){
			flex-grow: 0;
		}

		&:hover {
			.filter {
				opacity: 1;
			}
		}

		.filter {
			transition: $primaryAnimation;
			position: absolute;
			background-color: rgba(0, 0, 0, 0.5);


			&:after{
				content: "";
				position: absolute;
				background-image: url("/assets/images/SVG/icons/more.svg");
				width: 50px;
				height: 50px;
				top: 50%;
				left: 50%;
				transform: translate(-50%,-50%);
				background-repeat: no-repeat;
			}
		}

		.img-ctn {
			position: relative;
			display: block;
			border-radius: 2px;
			overflow: hidden;
			border-radius: 16px;
			height: 320px;

			&::before {
				content: "";
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				background-color: rgba(0, 0, 0, 0.2);
				border-radius: 24px;
				opacity: 0;
				transition: $primaryAnimation;
			}

			img {
				height: 100%;
				width: 100%;
				object-fit: cover;
				border-radius: 24px;
				transition: $primaryAnimation;
			}
		}

		.flex-wrap {
			display: flex;
			align-items: center;
			flex-wrap: wrap;

			.img-ctn {
				padding-left: 0;

				@media (max-width: 990px){
					padding-right: 0;
				}
			}
		}

		.article-info {
			position: relative;

			.title {
				margin: 0;
				margin-top: 32px;
				font-family: var(--font-primary);
				font-size: 34px;
				font-weight: 500;
				line-height: 42px;
				letter-spacing: -0.34px;
			}

			p {
				margin: 0;
			}

			.date {
				margin: 16px 0;
				@extend .-thumbnail-meta;
			}

			.description {
			}
		}

		@media (max-width: 992px) {
			.article-info {
			}
		}

		@media (max-width: 767px) {
			width: 100%;
			margin-right: 0;
		}

		&:hover {

			.img-ctn {
				&::before {
					opacity: 1;
				}

				img {
					width: 110%;
					height: 110%;
				}
			}
		}
	}

	.blog-button {
		position: absolute;
		right: 0;
		bottom: 0;
		height: 50px;
		width: 50px;
		background: var(--primary-color);
		color: $white;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: $transition;

		&:hover {
			background: var(--primary-color-darker);;
		}
	}

	.show-mobile {
		margin-top: 25px;
	}

	&.blog-list-5 {
		background-color: $white;

		.article-list-ctn {

			.grid {
				border-top: 1px solid $lighter-gray;
				padding: 40px 0;
				margin-bottom: 40px;

				&:last-child {
					margin-bottom: 0;
				}
			}

			.-right-ctn {
				padding-left: grid-space(math.div(1,12), 0);

				@media (max-width: 992px) {
					padding-left: 0;
				}
			}

			img {
				border-radius: 16px;
			}

			.title {
				color: var(--Main-Colors-Dark, #2D2F31);
				margin: 0;
				font-family: var(--font-primary);
				font-size: 24px;
				font-style: normal;
				font-weight: 400;
				line-height: 32px;
			}

			.-large {
				border-top: none;
				padding: 0;
				margin-bottom: 0;

				.title {
					font-size: 34px;
					font-weight: 500;
					line-height: 42px;
					letter-spacing: -0.34px;

					@media (max-width: 767px) {
						font-size: 24px;
						line-height: 32px;
					}
				}

				img {
					border-radius: 24px;
				}

			}

			.date {
				margin: 0;
				margin-top: 32px;
				@extend .-thumbnail-meta;
				color: var(--secondary-color);
			}

		}
	}
}
