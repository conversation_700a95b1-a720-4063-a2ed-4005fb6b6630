// Main footer
$footerMainBackground: $primary-color;
$footerMainPadding: 80px 0;
$footerMainLogoHeight: 92px;
$footerMainBorder: 0px none;

// Title link
$footerMainSubSectionLinkColor: $white;
$footerMainSubSectionLinkSize: 16px;
$footerMainSubSectionLinkWeight: 600;
$footerMainSubSectionLinkLineHeight: 22px;
$footerMainSubSectionLinkTextTransform: uppercase;

// Regular link
$footerMainItemLinkColor: $white;
$footerMainItemLinkSize: 14px;
$footerMainItemLinkLineHeight: 18px;

// Info
$footerMainInfoColor: $white;
$footerMainInfoSize: 14px;
$footerMainInfoWeight: 400;
$footerMainInfoLineHeight: 18px;

// Sub footer
$footerMainBackground: $primary-color;
$subFooterHeight: 60px;
$subFooterBorder: none;

$subFooterCopyrightColor: $lighter-gray;
$subFooterCopyrightFont: var(--font-primary);
$subFooterCopyrightSize: 13px;
$subFooterCopyrightLineHeight: normal;

$subFooterShareColor: var(--color-text);
$subFooterShareColorHover: var(--color-text);

#footer {
	position: relative;

	// Add opacity hover on all active links
	[href]:not([href=""]) {
		opacity: 1;
		transition: all .2s ease-in-out;
		&:hover { opacity: 0.7; }
	}

	// Links & site info
	.main-footer {
		background-color: $footerMainBackground;
		padding: $footerMainPadding;
		border-top: $footerMainBorder;

		.container {
			// display: flex;

			.footer-links {
				display: flex;
				flex-wrap: wrap;
				width: 100%;

				.footer-bloc {
					flex-basis: 50%;
					margin-bottom: 40px;

					// Regular links
					a {
						display: block;
						margin-bottom: 8px;
						color: $footerMainItemLinkColor;
						@extend .-cta-small;
						font-weight: 600;
					}

					// Title links
					h6 a {
						margin-bottom: 24px;
						color: $lighter-gray;
						font-size: 24px;
						font-weight: 500;
						line-height: normal;
					}

					h6.-alone {
						margin-top: 40px;
						margin-bottom: 0;
					}
				}
			}

			.footer-info {
				display: flex;
				flex-direction: column;
				
				h6 a {
					margin-bottom: 24px;
					color: $lighter-gray;
					font-size: 24px;
					font-weight: 500;
					line-height: normal;
				}

				.description-ctn,
				.location-ctn,
				.mail-ctn,
				.phone-ctn {
					width: auto;
					display: flex;
					align-items: flex-start;
					margin-top: 15px;

					a, p {
						color: $footerMainInfoColor;
						@extend .-cta-medium;
						margin-bottom: 24px;
					}

					.icon {
						color: $lighter-gray;
						padding-right: 24px;

						&-pin {
							font-size: 24px;
						}
					}
				}

				.location-ctn a {
					font-weight: 400;
					line-height: 28px;
					margin-top: -5px;
				}

				.share-ctn {
					display: flex;
					align-items: center;
					justify-content: flex-start;
					gap: 34px;
					margin-top: 40px;

					a {
						color: $white;
						font-size: 28px;
					}
				}
			}

			.footer-logos {
				display: flex;
				flex-direction: column;
				gap: 62px;
				border-left: 1px solid rgba(255, 255, 255, 0.233);
				padding-left: 46px;
				text-align: center;
			}
		}
	}

	// Copyright & social links
	.sub-footer {
		background-color: $footerMainBackground;
		height: $subFooterHeight;
		border-top: $subFooterBorder;
		padding-top: 15px;
		padding-bottom: 15px;

		.container {
			display: flex;
			align-items: center;
			gap: 10px;

			@media (max-width: 768px) {
				
				flex-direction: column;
				align-items: flex-start;

				.share-ctn {
					margin-left: unset !important;
				}
			}

			.share-ctn, .copyright-ctn {
				display: flex;

				a {
					
					&:hover {
						opacity: 0.7;
						transition: all .2s ease-in-out;
					}
				}
			}

			.copyright-ctn {
				gap: 10px;

				span, a {
					color: $subFooterCopyrightColor;
					font-family: $subFooterCopyrightFont;
					font-size: $subFooterCopyrightSize;
					line-height: $subFooterCopyrightLineHeight;
					margin-right: 40px;

					@media (max-width: 768px) {
						font-size: 14px;
						line-height: 20px;
						margin-right: 0;
					}

					&:last-child {
						margin-right: 0;
					}
				}
			}
		}
	}

	// Tablet
	@media (max-width: 992px) {
		.main-footer {
			padding: 50px 0;

			.container {
				gap: 40px 0;
			}
		}

		.sub-footer {
			height: auto;

			.container {
				.copyright-ctn {
					padding: 10px 0px;
					flex-direction: column;
				}
			}
		}
	}

	// Mobile
	@media (max-width: 539px) {
		.main-footer .container .footer-logos {
			border-left: none;
			padding-left: 0;
		}
	}

	// Mobile
	@media (max-width: 650px) {
		.footer-bloc {
			margin-bottom: 25px;
			 
			h6 a {
				margin-bottom: 15px !important;
			}
		}

		.sub-footer {
			.container {

				.copyright-ctn .copyright {
					margin-right: 0;
				}

				.share-ctn {
					margin-left: 0;
				}
			}
		}
	}
}

.popup-ctn {
	position: fixed;
	bottom: 40px;
	right: 40px;
	width: fit-content;
	height: auto;
	border-radius: 24px;
	background: $white;
	box-shadow: 0px 40px 80px 0px rgba(0, 0, 0, 0.20);
	padding: 32px;
	animation: slideIn 0.5s ease-out;
	z-index: 1000;

	&.closing {
		animation: slideOut 0.5s ease-in forwards;
	}

	@media (max-width: 768px) {
		bottom: 0;
		right: 0;
		left: 0;
		width: 100%;
		border-radius: 24px 24px 0 0;
		padding: 24px;
		box-shadow: 0px -20px 40px 0px rgba(0, 0, 0, 0.20);
	}

	.popup-close-ctn {
		position: absolute;
		top: 20px;
		right: 20px;
		cursor: pointer;
		background: none;
		border: none;
		padding: 8px;
		margin: 0;
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 1001;
		width: 40px;
		height: 40px;
		border-radius: 50%;
		transition: background-color 0.2s ease;

		&:hover {
			background-color: rgba(0, 0, 0, 0.05);
		}

		.close-icon {
			width: 24px;
			height: 24px;
			pointer-events: none;
		}
	}

	.popup-content {
		max-width: 450px;
		padding-right: 35px;
		position: relative;
		z-index: 1;
		
		@media (max-width: 768px) {
			max-width: 100%;
		}
		
		.popup-title {
			color: $black;
			font-family: var(--font-secondary);
			font-size: 26px;
			font-weight: 500;
			line-height: 30px;
			margin: 0;

			@media (max-width: 768px) {
				font-size: 24px;
				line-height: 28px;
			}
		}

		.popup-text {
			@extend .--small;
			margin: 32px 0;

			@media (max-width: 768px) {
				margin: 24px 0;
			}
		}

		.main-button {
			width: fit-content;
		}
	}
}

@keyframes slideIn {
	from {
		transform: translateX(100%);
		opacity: 0;
	}
	to {
		transform: translateX(0);
		opacity: 1;
	}
}

@keyframes slideOut {
	from {
		transform: translateX(0);
		opacity: 1;
	}
	to {
		transform: translateX(100%);
		opacity: 0;
	}
}

@media (max-width: 768px) {
	@keyframes slideIn {
		from {
			transform: translateY(100%);
			opacity: 0;
		}
		to {
			transform: translateY(0);
			opacity: 1;
		}
	}

	@keyframes slideOut {
		from {
			transform: translateY(0);
			opacity: 1;
		}
		to {
			transform: translateY(100%);
			opacity: 0;
		}
	}
}