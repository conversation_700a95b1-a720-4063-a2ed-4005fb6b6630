.paginiation-controls {
	.ngx-pagination {
    @include flex();
    gap: 6px;
		margin: 50px auto;
		padding: 0;
		text-align: center;

		li {
      width: 40px;
      height: 40px;
      padding: 0;
			border-radius: 6px;
			border: 2px solid var(--primary-color);

      > span, > a { 
        padding: 0;
        @extend .-cta-medium;
        color: var(--primary-color);
        display: flex;
        align-items: center;
        justify-content: center;
      }

      >a { height: 100%; }

			&.disabled { display: none; }

			&.current {
				background: var(--primary-color);
        border-color: var(--primary-color);
				span { color: white; }

        display: flex;
        align-items: center;
        justify-content: center;
			}
		}

		a:hover { 
      background: var(--primary-color);
      color: white;

      &:after, &::before { 
        color: white;
      }
    }

		.pagination-previous, .pagination-next {
			border: 2px solid var(--primary-color);

      a { 
        height: 100%;
        
        &:after, &::before { 
          position: relative;
          top: -1px;
          margin: 0;
          font-family: 'icomoon';
          font-size: 14px;
          color: var(--primary-color);
        }

        &:hover {
          color: white;

          &:after, &::before { 
            color: white;
          }
        }
      }
    }

		.pagination-previous a::before { content: "\e909"; }
		.pagination-next a::after { content: "\e90a"; }
	}
}
