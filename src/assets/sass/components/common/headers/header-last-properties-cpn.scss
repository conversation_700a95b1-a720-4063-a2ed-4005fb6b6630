$headerSubMultiColumnWidth: 440px;

$lastPropertiesBorder: 1px solid rgba(#F0EDE3, 0.3);
$lastPropertiesTitle: $white;
$lastPropertiesName: var(--color-text);
$lastPropertiesDescription: #353f46;

#header,
#header2 {
    .main-header {
        .main-menu-ctn {
            .main-menu {
                .item-menu:hover {
                    .secondary-ul {
                        pointer-events: all !important;
                    }
                }

                .secondary-ul.multi-column {
                    display: flex;
                    width: $headerSubMultiColumnWidth;
                    flex-direction: column;
                    padding-top: 0;
                    transition: none !important;
                    pointer-events: none !important;
    
                    @media (max-width: 1200px) {
                        width: 360px;
                    }
        
                    @media (max-width: 1100px) {
                        width: 290px;
                    }
            
                    &:hover {
                        pointer-events: auto !important;
                    }
                
                    ul {
                        padding: 0;

                        &:first-child {
                            margin-bottom: 10px;
                            margin-top: 10px;
                        }
                    }
    
                    .header-last-properties {
                        border-top: $lastPropertiesBorder;
    
                        .title {
                            @extend .-thumbnail-meta;
                            color: $black;
                            text-transform: uppercase;
                            margin-top: 18px;
                            margin-bottom: 10px;
                        }
    
                        .list {
                            margin: 0;
                
                            li {
                                display: flex;
                                padding: 0;
                                margin-bottom: 15px;
                                color: $subHeaderBG2;
                                border-bottom: none;
                    
                                &:last-child {
                                    border: none;
                                    margin-bottom: 0;
                                }
                
                                .image-ctn {
                                    display: flex;
                                    background-color: $black;
                                    margin: 0;
                                    width: 80px;
                                    height: 55px;
                                    margin-right: 20px;
    
                                    img {
                                        width: 100%;
                                        height: 100%;
                                        object-fit: cover;
                                        transition: all 0.2s ease;
                                    }
                                }
                    
                                a {
                                    padding: 0;
                                    color: $lastPropertiesName;
                                    transition: all 0.2s ease;
                                    font-size: 14px;
                                    font-weight: 700;
                                    line-height: normal;
                
                                    small {
                                        display: block;
                                        margin-top: 4px;
                                        color: $lastPropertiesDescription;
                                        font-size: 12px;
                                        font-weight: 400;
                                        line-height: 15px;
                                    }
                
                                    &:hover { 
                                        opacity: 1; 
                                    }
                                }   
                                
                                &:hover { 
                                    a {
                                        color: var(--color-text);
                                        opacity: 0.6;
                                    }
                
                                    img {
                                        opacity: 0.6;
                                    }
                                }
                    
                            }
                        }
                    }
                
                    .properties-loader {
                        display: flex;
                        width: $headerSubMultiColumnWidth;
                        min-height: 50px;
                
                        @media (max-width: 1200px) {
                            width: 360px;
                        }
                    }
                }
            }
            
        }
    }
}

#header2 {
    .main-header {
        .main-menu-ctn {
            .main-menu {
                .secondary-ul.multi-column {
                    left: -150px;

                    &::before {
                        left: 160px;
                    }
                }
            }
        }
    }
}
