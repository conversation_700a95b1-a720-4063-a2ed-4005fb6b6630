/*
	Sub Header Component
*/
$subHeaderHeight1: 45px;
$subHeaderBG1: white;
$subHeaderLinkSize1: 13px;
$subHeaderLinkColor1: var(--color-text);

$subHeaderFavoriteBorderLeft1: 1px solid #D8D8D8;
$subHeaderFavoritePadding1:0 20px;
$subHeaderFavoriteSize1: 14px;

$subHeaderNumberBG1: var(--primary-color);
$subHeaderNumberBGHover1: var(--primary-color-darker);;
$subHeaderNumberBorderLeft1: 1px solid #D8D8D8;
$subHeaderNumberPadding1: 0 17px;
$subHeaderNumberColor1: white;
$subHeaderNumberSize1: 18px;
$subHeaderNumberWeight1: 900;
$subHeaderNumberOpacity1: 0.96;
$subHeaderNumberPhoneBorder1: none;
$subHeaderNumberPhoneRadius1: 0px;

/*
	Header Component
*/
$headerHeight1: 120px;
$headerBG1: $white;
$headerBorderBottom1: 1px solid #DDDDDD;
$headerLinkPaddingRight1: 32px;
$headerLinkSize1: 14px;
$headerLinkWeight1: 700;
$headerLinkLineHeight1: 17px;
$headerLinkColor1: $black;
$headerLinkTransform1: uppercase;
$headerSubMenuBG1: #F1ECE1;
$headerSubMenuLinkBGHover1: transparent;
$headerSubMenuLinkColor1: var(--color-text);
$headerSubMenuLinkColorHover1: var(--color-text);
$headerSubMenuLinkLineHeight1: 20px;
$headerSubMenuLinkSize1: 14px;
$headerSubLinkTransform1: none;

/*
	Transparent Header
*/
$transparentSubHeaderLinkColor1: $white;
$transparentSubHeaderNumberColor1: $white;
$transparentHeaderLinkColor1: $white;

/*
	Special Color Header
*/
$specialSubHeaderLinkColor1: blue;
$specialSubHeaderNumberColor1: blue;
$specialHeaderLinkColor1: blue;

#header {
	position: relative;
	z-index: 200;
	//border-bottom: $headerBorderBottom1;

	.sub-header {
		background: $subHeaderBG1;
		@include clearfix;

		ul {
			float: right;
			margin: 0;
			color: $subHeaderLinkColor1;
			line-height: $subHeaderHeight1;
			display: flex;
			padding-left: 0;
			background: var(--color-bg);

			li {
				display: inline-block;

				&:first-child {
					padding-right: 15px;
				}

				@media (max-width: 1400px) {
					&:first-child {
						padding-right: 10px;
					}
				}

				a {
					color: $subHeaderLinkColor1;
					font-size: $subHeaderLinkSize1;
				}

				&.language {
					background-color: white;
				}
			}
		}

		.english-fake{
			display:none;
		}

		.switch:hover{
			cursor: pointer;
		}

		.favorite {
			padding: $subHeaderFavoritePadding1;
			border-left: $subHeaderFavoriteBorderLeft1;

			a {
				font-size: $subHeaderFavoriteSize1;
				font-weight: 700;
				display: flex;
				align-items: center;

				i {
					margin-right: 10px;
					font-size: 20px;
				}
			}
		}

		.number-ctn {
			background: $subHeaderNumberBG1;
			padding: $subHeaderNumberPadding1;
			border-left: $subHeaderNumberBorderLeft1;
			transition: $primaryAnimation;

			&:hover {
				background: $subHeaderNumberBGHover1;
				color: $primaryButtonTextColorHover;
			}

			a {
				color: $subHeaderNumberColor1;
				font-size: $subHeaderNumberSize1;
				font-weight: $subHeaderNumberWeight1;
				opacity: $subHeaderNumberOpacity1;

				i {
					margin-right: 10px;
					font-size: var(--font-size-small);
					border: $subHeaderNumberPhoneBorder1;
					border-radius: $subHeaderNumberPhoneRadius1;
				}
			}
		}

		.share-ctn {
			padding-left: 30px;
			padding-right: 30px;
			border-left: 1px solid #D8D8D8;

			@media (max-width: 1400px) {
				display: none;
			}

			ul {
				margin: 0;
				padding-left: 0;
				display: flex;

				li {
					margin-right: 15px;
					padding-right: 0;

					&:last-child {
						margin-right: 0;
					}
				}

				a {
					display: inline-block;
					font-size: 16px;
					color: var(--color-text);
					transition: $primaryTextAnimation;

					&:hover {
						color: #D8D8D8;
					}
				}
			}
		}
	}
	.main-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: $headerHeight1;
		padding: 24px 48px;
		background-color: $headerBG1;

		@media (max-width: 1095px) {
			padding: 24px 20px;
		}

		.special-logo {
			width: 119px;

			&.-black {
				display: block;
			}

			&.-white {
				display: none;
			}
		}

		.logo-ctn {
			height: 66px;
			display: flex;
			align-items: center;

			@media (max-width: 420px) {
				height: 43px;
			}

			> a {
				height: 66px;
				display: flex;
				align-items: center;

				@media (max-width: 420px) {
					height: 43px;
				}
			}

			img {
				max-height: 100%;

				@media (max-width: 420px) {
					height: 43px;
					max-height: 43px;
				}
			}

			.special-logo {
				margin-left: 25px;
			}

			&.-white {
				display: none;
			}

			&.-black {
				display: block;
			}
		}

		.primary-header {
			display: flex;
			align-items: center;
			justify-content: space-between;
		}

		.secondary-header {
			display: flex;
			align-items: center;
			gap: 56px;

			@media (max-width: 1400px) {
				display: none;
			}

			a {
				font-size: 18px;
				font-weight: 600;
				line-height: normal;
				display: flex;
				align-items: center;
				gap: 10px;
			}

			.switch {
				cursor: pointer;
			}
		}

		.main-menu-ctn {
			@media (max-width: 1400px) {
				display: none;
			}

			border-left: 1px solid $black;
			padding-left: 40px;
			margin-left: 40px;

			.main-menu {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding-left: 0;
				list-style-type: none;
				position: relative;
				z-index: 1;
				margin: 0;
				flex-flow: row wrap;
				background: transparent;

				.item-menu {
					position: relative;
					display: inline-block;
					padding-right: $headerLinkPaddingRight1;
					line-height: 66px;

					@media (max-width: 1300px) {
						padding-right: 25px;
					}

					@media (max-width: 1200px) {
						padding-right: 15px;
					}

					&:last-child {
						padding-right: 0;
					}

					&.active-child{
						> a {
							background: $headerSubMenuLinkBGHover1;
							color: $headerSubMenuLinkColorHover1;
							opacity: 0.5;
						}
					}

					.secondary-ul {
						background: $lighter-gray;
						margin: 0px;
						width: 220px;
						padding: 16px 20px;
						left: 0;
						position: absolute;
						z-index: -1000;
						opacity: 0;
						display: none;
						border-radius: 16px;

						a {
							color: var(--color-text);
							display: block;
							font-size: 17px;
							font-weight: 600;
							line-height: normal;
							text-decoration: none;
							padding: 8px 0;
						}

						&:before {
							content: "";
							width: 0;
							height: 0;
							border-left: 10px solid transparent;
							border-right: 10px solid transparent;
							border-bottom: 10px solid $lighter-gray;
							position: absolute;
							top: -10px;
							left: 15px;
						}
					}

					> a {
						font-size: 17px;
						font-weight: 600;
						line-height: normal;
						text-decoration: none;
						color: $headerLinkColor1;
						opacity: 1;
						transition: $transition;
						text-transform: initial;

						i {
							margin-left: 6px;
							font-size: 11px;
						}

						@media (max-width: 1200px) {
							font-size: 13px;
						}

						@media (max-width: 1095px) {
							font-size: 11px;
						}

						&.active {
							opacity: 0.5;
						}
					}

					&:hover {
						cursor: pointer;

						> a {
							opacity: 0.5;
						}

						.secondary-ul {
							z-index: 200;
							opacity: 1;
							transition: z-index 0.5s step-start, opacity 0.5s ease;
							display: block;

							li {
								display: block;

								a {

									&.active, &:hover {
										background: $headerSubMenuLinkBGHover1;
										color: $headerSubMenuLinkColorHover1;
										opacity: 0.5;
									}
								}
							}
						}
					}

				}
			}
		}
	}

	.header-menu-toggle {
		display: none;
		position: relative;
		right: 0;
		width: 30px;
		height: 25px;
		transition: all 0.5s ease;

		@media (max-width: 1400px) {
			display: inline-block;
		}

		span {
			width: 100%;
			height: 3px;
			background-color: black;
			position: absolute;
			top: 0;
			right: 0;
			left: 0;
			-ms-filter: none;
			-webkit-filter: none;
			filter: none;
			opacity: 1;
			-webkit-transform-origin: center;
			-ms-transform-origin: center;
			transform-origin: center;
			transition: all .25s ease-in-out;

			&:nth-child(2){
				top: 10px;
			}

			&:nth-child(3){
				top: 20px;
			}
		}
	}

	&.transparent {
		.logo-ctn {
			&.-white {
				display: block;
			}

			&.-black {
				display: none;
			}
		}

		.main-header {

			.main-menu-ctn {
				border-left-color: $white;
			}

			.secondary-header a {
				color: $white;
			}

			.secondary-header {
				.special-logo {
					width: 119px;
					&.-white {
						display: block;
					}

					&.-black {
						display: none;
					}
				}
			}
		} 

		.header-menu-toggle {
			span {
				background-color: $white;
			}
		}

		.sub-header {
			background: transparent;

			ul {
				li {
					a {
						color: $transparentSubHeaderLinkColor1;
					}
				}
			}

			.number-ctn {
				a {
					color: $transparentSubHeaderNumberColor1;
				}
			}
		}
		.main-header {
			background: transparent;

			.main-menu-ctn {
				.main-menu {
					.item-menu {
						> a {
							color: $transparentHeaderLinkColor1;
						}
					}
				}
			}
		}
	}

	&.special {
		.sub-header {
			background: transparent;

			ul {
				li {
					a {
						color: $specialSubHeaderLinkColor1;
					}
				}
			}

			.number-ctn {
				a {
					color: $specialSubHeaderNumberColor1;
				}
			}
		}
		.main-header {
			background: transparent;

			.main-menu-ctn {
				.main-menu {
					.item-menu {
						> a {
							color: $specialHeaderLinkColor1;
						}
					}
				}
			}
		}
	}
}

//Opened Menu
body {
	&.-open-menu {
		#header {
			.header-menu-toggle {
				height: 25px;

				span {
					&:first-child {
						transform: rotate(45deg);
						top: 12px;
					}

					&:nth-child(2) {
						opacity: 0;
					}

					&:nth-child(3) {
						transform: rotate(-45deg);
						top: 12px;
					}
				}
			}
		}
	}
}
