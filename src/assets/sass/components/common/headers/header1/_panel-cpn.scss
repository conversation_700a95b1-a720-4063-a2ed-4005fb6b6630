/*
	Panel Component
*/

$panelMainSubSectionLinkColor: $white;
$panelMainSubSectionLinkFont: var(--font-secondary);
$panelMainSubSectionLinkSize: 22px;
$panelMainSubSectionLinkWeight: normal;
$panelMainSubSectionLinkLineHeight: 27px;
$panelMainSubSectionLinkTextTransform: capitalize;

$panelMainItemLinkColor: #EEEEEE;
$panelMainItemLinkFont: var(--font-secondary);
$panelMainItemLinkSize: 14px;
$panelMainItemLinkLineHeight: 24px;


body {
	&.-open-menu {
		.panel-cpn {
			right: 0;

			&.-full {
				top: 0;
				right: inherit;
			}
		}

		.panel-backdrop {
			z-index: 5000;
			opacity: 1;
			transition: z-index 0.5s step-start, opacity 0.5s ease;
		}
	}
}


.panel-backdrop {
	position: absolute;
	left: 0;
	top: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(37,37,37,0.7);
	z-index: -1;
	opacity: 0;
	transition: z-index 0.5s step-end, opacity 0.5s ease;
}

.panel-cpn {
	position: fixed;
	top: 0;
	width: 315px;
	height: 100vh;
	background: #1E2021;
	z-index: 5000;
	right: -320px;
	transition: all 300ms cubic-bezier(0.190, 1.000, 0.560, 1.000);
	overflow-y: scroll;

	@media (min-width: 767px) {
		width: 460px;
		right: -465px;
	}

	.close-button {
		position: absolute;
		top: 30px;
		right: 27px;
		width: 30px;
		height: 25px;
		transition: all 0.5s ease;

		span {
			width: 100%;
			height: 3px;
			background-color: white;
			position: absolute;
			top: 0;
			right: 0;
			left: 0;
			-ms-filter: none;
			-webkit-filter: none;
			filter: none;
			opacity: 1;
			-webkit-transform-origin: center;
			-ms-transform-origin: center;
			transform-origin: center;
			transition: all .25s ease-in-out;

			&:first-child {
				transform: rotate(45deg);
				top: 12px;
			}

			&:nth-child(2){
				display: none;
			}

			&:nth-child(3) {
				transform: rotate(-45deg);
				top: 12px;
			}
		}
	}

	.switch {
		color: #FFFFFF;
		font-size: 14px;
		line-height: 17px;
		padding-left: 20px;
    cursor: pointer;
	}

	.panel-menu {
		margin: 0;
		padding: 100px 30px 10px 20px;

		.main-item {
			padding-bottom: 15px;
			margin-bottom: 15px;
			border-bottom: 1px solid rgba(231,231,231,0.2);

			&:last-child {
				border-bottom: none;
			}

			@media (min-width: 992px) {
				padding-bottom: 25px;
				margin-bottom: 25px;
			}

			> span, > a {
				position: relative;
				display: block;
				color: $white;
				// font-family: $primaryFont;
				font-size: 17px;
				line-height: 19px;

				@media (min-width: 992px) {
					font-size: 22px;
					line-height: 27px;
				}
			}

			a.active {
				opacity: 0.5;
			}
		}

		.-dropdown {
			position: relative;

			> span {
				&:after {
					position: absolute;
					top: 50%;
					transform: translate(0%, -50%);
					font-size: 12px;
					right: 0;
					content: "\e90e";
					font-family: 'icomoon' !important;
					speak: none;
					font-style: normal;
					font-weight: normal;
					font-variant: normal;
					text-transform: none;
					line-height: 1;
				}
			}

			[data-toggle="dropdown"] {
				display: block;
				color: white;
			}

			> .dropdown-menu {
				padding-left: 0;
				max-height: 0;
				overflow: hidden;

				li {
					padding: 0;

					&:first-child{
						margin-top: 15px;
					}

					a {
						display: block;
						padding: 10px 10px 10px 0px;
						color: $white;
						font-size: var(--font-size-cta-small);
						line-height: 17px;

						&.active {
							opacity: 0.5;
						}
					}
				}
			}
			> input[type="checkbox"] {
				opacity: 0;
				display: block;
				position: absolute;
				top: 0;
				width: 100%;
				height: 25px;
				cursor: pointer;
				z-index: 20;

				&:checked ~ .dropdown-menu {
					max-height: 9999px;
					display: block;
				}
			}

			&.active-child {
				> span {
					opacity: 0.5;
				}
			}
		}
	}

	.share-ctn {
		margin: 0;
		padding-left: 20px;
		padding-top: 40px;
		color: white;
		font-size: 15px;

		@media (min-width: 992px) {
			font-size: 19px;
		}

		ul {
			margin: 0;
			padding-left: 0;
			display: flex;

			li {
				margin-right: 20px;

				@media (min-width: 992px) {
					margin-right: 35px;
				}

				&:last-child {
					margin-right: 0;
				}
			}

			a {
				display: inline-block;
				color: rgba(255,255,255,0.5);
				transition: $primaryTextAnimation;
			}
		}
	}

	&.-full {
		width: 100%;
		right: 0;
		top: -100%;
		display: flex;
		align-items: center;
		justify-content: center;

		.number-ctn {
			position: absolute;
			top: 30px;
			right: 108px;
			padding: 0 0 40px 0;
			transition: $primaryAnimation;
			list-style: none;

			a {
				color: white;
				font-size: 18px;
				font-weight: 900;
				// font-family: $primaryFont;
				opacity: 0.96;

				@media (max-width: 1400px) {
					font-size: 15px;
					line-height: 18px;
				}

				i {
					margin-right: 10px;
					font-size: 16px;
				}
			}
		}

		li {
			list-style: none;
		}

		.sub-section-link {
			margin-bottom: 17px;

			> a,span {
				color: $panelMainSubSectionLinkColor;
				font-family: $panelMainSubSectionLinkFont;
				font-size: $panelMainSubSectionLinkSize;
				font-weight: $panelMainSubSectionLinkWeight;
				line-height: $panelMainSubSectionLinkLineHeight;
        		text-transform: $panelMainSubSectionLinkTextTransform;
			}

			&.-second {
				margin-top: 40px;
			}
		}

		.item-link {
			> a {
				color: $panelMainItemLinkColor;
				font-family: $panelMainItemLinkFont;
				font-size: $panelMainItemLinkSize;
				line-height: $panelMainItemLinkLineHeight;
			}
		}

		.switch {
			display: block;
			margin-top: 40px;
			padding-left: 0;
		}

		.info-ctn {
			padding-top: 20px;
			opacity: 0.6;

			i {
				display: none;
				margin-left: 15px;
			}

			a {
				display: block;
				margin-top: 20px;
				color: #FFFFFF;
				font-size: 15px;
				line-height: 18px;
			}
		}

		.share-ctn {
			padding-left: 0;
		}
	}
}
