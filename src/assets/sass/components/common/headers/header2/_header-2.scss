/*
	Sub Header Component
*/
$subHeaderHeight2: 45px;
$subHeaderBG2: #FFFFFF;
$subHeaderLinkSize2: 14px;
$subHeaderLinkColor2: #4A4A4A;

$subHeaderBorderShare2: none;

$subHeaderFavoriteBorderLeft2: none;
$subHeaderFavoritePadding2:0 20px;
$subHeaderFavoriteSize2: 14px;

$subHeaderNumberBG2: #FFFFFF;
$subHeaderNumberBGHover2: #FFFFFF;
$subHeaderNumberBorderLeft2: none;
$subHeaderNumberPadding2: 0 40px 0 17px;
$subHeaderNumberColor2: #2D2F31;
$subHeaderNumberSize2: 18px;
$subHeaderNumberWeight2: 900;
// $subHeaderNumberFont2: $primaryFont;
$subHeaderNumberOpacity2: 0.96;
$subHeaderNumberPhoneBorder2: none;
$subHeaderNumberPhoneRadius2: 0px;

/*
	Header Component
*/
$headerHeight2: 80px;
$headerBG2: $white;
$headerBorderBottom2: 1px solid #DDDDDD;
$headerLinkPaddingRight2: 35px;
$headerLinkSize2: 14px;
// $headerLinkFont2: $primaryFont;
$headerLinkWeight2: normal;
$headerLinkLineHeight2: 17px;
$headerLinkColor2: #4A4A4A;
$headerLinkTransform2: none;
$headerSubMenuBG2: #eee;
$headerSubMenuLinkBGHover2: transparent;
$headerSubMenuLinkColor2: var(--color-text);
$headerSubMenuLinkColorHover2: var(--color-text);
$headerSubMenuLinkLineHeight2: 20px;
$headerSubMenuLinkSize2: 14px;
$headerSubLinkTransform2: none;


/*
	Transparent Header
*/
$transparentSubHeaderLinkColor2: $white;
$transparentSubHeaderNumberColor2: $white;
$transparentHeaderLinkColor2: $white;

/*
	Special Color Header
*/
$specialSubHeaderLinkColor2: blue;
$specialSubHeaderNumberColor2: blue;
$specialHeaderLinkColor2: blue;

#header2 {
	position: relative;
	z-index: 200;
	border-bottom: $headerBorderBottom2;

	.sub-header {
		background: $subHeaderBG2;
		@include clearfix;

		ul {
			float: right;
			margin: 0;
			color: $subHeaderLinkColor2;
			line-height: $subHeaderHeight2;
			display: flex;
			padding-left: 0;

			li {
				display: inline-block;

				&:first-child {
					padding-right: 15px;
				}

				@media (max-width: 992px) {
					&:first-child {
						padding-right: 10px;
					}
				}

				a {
					color: $subHeaderLinkColor2;
					font-size: $subHeaderLinkSize2;
				}
			}
		}

		.switch:hover{
			cursor: pointer;
		}

		.favorite {
			padding: $subHeaderFavoritePadding2;
			border-left: $subHeaderFavoriteBorderLeft2;

			a {
				font-size: $subHeaderFavoriteSize2;
				font-weight: 700;
				display: flex;
				align-items: center;

				i {
					margin-right: 10px;
					font-size: 20px;
				}
			}
		}

		.number-ctn {
			background: $subHeaderNumberBG2;
			padding: $subHeaderNumberPadding2;
			border-left: $subHeaderNumberBorderLeft2;
			transition: $primaryAnimation;

			&:hover {
				background: $subHeaderNumberBGHover2;
				color: $primaryButtonTextColorHover;
			}

			a {
				color: $subHeaderNumberColor2;
				font-size: $subHeaderNumberSize2;
				font-weight: $subHeaderNumberWeight2;
				// font-family: $subHeaderNumberFont2;
				opacity: $subHeaderNumberOpacity2;

				i {
					margin-right: 10px;
					font-size: var(--font-size-small);
					border: $subHeaderNumberPhoneBorder2;
					border-radius: $subHeaderNumberPhoneRadius2;
				}
			}
		}

		.share-ctn {
			padding-left: 30px;
			padding-right: 30px;
			border-left: $subHeaderBorderShare2;

			@media (max-width: 992px) {
				display: none;
			}

			ul {
				margin: 0;
				padding-left: 0;
				display: flex;
				// align-item: center;

				li {
					margin-right: 15px;
					padding-right: 0;

					&:last-child {
						margin-right: 0;
					}
				}

				a {
					display: inline-block;
					font-size: 16px;
					color: var(--color-text);
					transition: $primaryTextAnimation;

					&:hover {
						color: #D8D8D8;
					}
				}
			}
		}
	}
	.main-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: $headerHeight2;
		padding: 0 40px 10px;
		background-color: $headerBG2;

		@media (max-width: 1095px) {
			padding: 10px 20px;
		}

		.special-logo {
			fill: $black;
			width: 67px;
      		max-height: 55px;
		}

		.logo-ctn {
			height: 58px;
			display: flex;
			align-items: center;

			> a {
				height: 58px;
				display: flex;
				align-items: center;
			}

			img {
				max-height: 100%;
			}

			.special-logo {
				margin-left: 25px;
			}
		}

		.main-menu-ctn {
			@media (max-width: 992px) {
				display: none;
			}

			.main-menu {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding-left: 0;
				list-style-type: none;
				position: relative;
				z-index: 1;
				margin: 0;
				flex-flow: row wrap;
				background: transparent;

				.item-menu {
					position: relative;
					display: inline-block;
					padding-right: $headerLinkPaddingRight2;

					@media (max-width: 1300px) {
						padding-right: 25px;
					}

					@media (max-width: 1200px) {
						padding-right: 15px;
					}

					&:last-child {
						padding-right: 0;
					}

					&.active-child{
						> a {
							background: $headerSubMenuLinkBGHover2;
							color: $headerSubMenuLinkColorHover2;
							opacity: 0.5;
						}
					}

					.secondary-ul {
						background: $headerSubMenuBG2;
						margin: 0px;
						width: 200px;
						padding-left: 0;
						padding-top: 15px;
						padding-bottom: 15px;
						left: 0;
						position: absolute;
						z-index: -1000;
						opacity: 0;
						display: none;

						&:before {
							content: "";
							width: 0;
							height: 0;
							border-left: 10px solid transparent;
							border-right: 10px solid transparent;
							border-bottom: 10px solid $headerSubMenuBG2;
							position: absolute;
							top: -10px;
							left: 15px;
						}
					}

					> a {
						// font-family: $headerLinkFont2;
						font-size: $headerLinkSize2;
						color: $headerLinkColor2;
						font-weight: $headerLinkWeight2;
						line-height: 66px;
						text-transform: $headerLinkTransform2;
						opacity: 1;
						transition: $transition;

						i {
							margin-left: 10px;
							font-size: 11px;
						}

						@media (max-width: 1200px) {
							font-size: 13px;
						}

						@media (max-width: 1095px) {
							font-size: 11px;
						}

						&.active {
							opacity: 0.5;
						}
					}

					&:hover {
						cursor: pointer;

						> a {
							opacity: 0.5;
						}

						.secondary-ul {
							z-index: 200;
							opacity: 1;
							transition: z-index 0.5s step-start, opacity 0.5s ease;
							display: block;

							li {
								display: block;

								a {
									display: block;
									// font-family: $headerLinkFont2;
									font-size: $headerSubMenuLinkSize2;
									color: $headerSubMenuLinkColor2;
									font-weight: $headerLinkWeight2;
									line-height: $headerSubMenuLinkLineHeight2;
									text-transform: $headerSubLinkTransform2;
									padding: 10px 30px 10px 30px;

									&.active, &:hover {
										background: $headerSubMenuLinkBGHover2;
										color: $headerSubMenuLinkColorHover2;
										opacity: 0.5;
									}
								}
							}
						}
					}

				}
			}
		}
	}

	.header-menu-toggle {
		display: none;
		position: relative;
		right: 0;
		width: 30px;
		height: 25px;
		transition: all 0.5s ease;

		@media (max-width: 992px) {
			display: inline-block;
		}

		span {
			width: 100%;
			height: 3px;
			background-color: black;
			position: absolute;
			top: 0;
			right: 0;
			left: 0;
			-ms-filter: none;
			-webkit-filter: none;
			filter: none;
			opacity: 1;
			-webkit-transform-origin: center;
			-ms-transform-origin: center;
			transform-origin: center;
			transition: all .25s ease-in-out;

			&:nth-child(2){
				top: 10px;
			}

			&:nth-child(3){
				top: 20px;
			}
		}
	}

	&.transparent {
		.sub-header {
			background: transparent;

			ul {
				li {
					a {
						color: $transparentSubHeaderLinkColor2;
					}
				}
			}

			.number-ctn {
				a {
					color: $transparentSubHeaderNumberColor2;
				}
			}
		}
		.main-header {
			background: transparent;

			.main-menu-ctn {
				.main-menu {
					.item-menu {
						> a {
							color: $transparentHeaderLinkColor2;
						}
					}
				}
			}
		}
	}

	&.special {
		.sub-header {
			background: transparent;

			ul {
				li {
					a {
						color: $specialSubHeaderLinkColor2;
					}
				}
			}

			.number-ctn {
				a {
					color: $specialSubHeaderNumberColor2;
				}
			}
		}
		.main-header {
			background: transparent;

			.main-menu-ctn {
				.main-menu {
					.item-menu {
						> a {
							color: $specialHeaderLinkColor2;
						}
					}
				}
			}
		}
	}
}

//Opened Menu
body {
	&.-open-menu {
		#header {
			.header-menu-toggle {
				height: 25px;

				span {
					&:first-child {
						transform: rotate(45deg);
						top: 12px;
					}

					&:nth-child(2) {
						opacity: 0;
					}

					&:nth-child(3) {
						transform: rotate(-45deg);
						top: 12px;
					}
				}
			}
		}
	}
}
