/*
	Sub Header Component
*/
$subHeaderLinkSize3: 14px;
$subHeaderLinkColor3: #4A4A4A;

$subHeaderBorderShare3: none;

$subHeaderFavoriteBorderLeft3: none;
$subHeaderFavoritePadding3:0 20px;
$subHeaderFavoriteSize3: 14px;

$subHeaderNumberBG3: #FFFFFF;
$subHeaderNumberBGHover3: #FFFFFF;
$subHeaderNumberBorderLeft3: none;
$subHeaderNumberPadding3: 0 0 0 17px;
$subHeaderNumberColor3: var(--primary-color);
$subHeaderNumberSize3: 18px;
$subHeaderNumberWeight3: 900;
$subHeaderNumberOpacity3: 0.96;
$subHeaderNumberPhoneBorder3: none;
$subHeaderNumberPhoneRadius3: 0px;

/*
	Header Component
*/

$headerLinkSize3: 14px;
$headerLinkWeight3: normal;
$headerLinkLineHeight3: 17px;
$headerLinkColor3: #4A4A4A;
$headerLinkTransform3: none;
$headerSubMenuBG3: #eee;
$headerSubMenuLinkBGHover3: transparent;
$headerSubMenuLinkColor3: var(--color-text);
$headerSubMenuLinkColorHover3: var(--color-text);
$headerSubMenuLinkLineHeight3: 20px;
$headerSubMenuLinkSize3: 14px;
$headerSubLinkTransform3: none;

#header3 {
	position: relative;
	z-index: 200;

	@media (max-width: 992px) {
		height: 105px;
	}

	.full-header {
		position: relative;
		padding: 25px 40px 0;
		display: flex;
		justify-content: space-between;

		@media (max-width: 992px) {
			align-items: center;
		}

		.special-logo {
			height: 50px;
		}

		.logo-ctn {
			position: absolute;
			top: 30px;
			left: 50%;
			transform: translate(-50%, 0%);

			a {
				display: block;
			}
		}

		.sub-head-menu {
			float: right;
			margin: 0;
			color: $subHeaderLinkColor3;
			display: flex;
			padding-left: 0;

			@media (max-width: 992px) {
				display: none;
			}

			li {
				display: inline-block;

				&:first-child {
					padding-right: 15px;
				}

				@media (max-width: 992px) {
					&:first-child {
						padding-right: 10px;
					}
				}

				a {
					color: $subHeaderLinkColor3;
					font-size: $subHeaderLinkSize3;
				}
			}
	
			.switch:hover{
				cursor: pointer;
			}
	
			.favorite {
				padding: $subHeaderFavoritePadding3;
				border-left: $subHeaderFavoriteBorderLeft3;
	
				a {
					font-size: $subHeaderFavoriteSize3;
					font-weight: 700;
					display: flex;
					align-items: center;
	
					i {
						margin-right: 10px;
						font-size: 20px;
					}
				}
			}
	
			.number-ctn {
				background: $subHeaderNumberBG3;
				padding: $subHeaderNumberPadding3;
				border-left: $subHeaderNumberBorderLeft3;
				transition: $primaryAnimation;
	
				&:hover {
					background: $subHeaderNumberBGHover3;
					color: $primaryButtonTextColorHover;
				}
	
				a {
					color: $subHeaderNumberColor3;
					font-size: $subHeaderNumberSize3;
					font-weight: $subHeaderNumberWeight3;
					opacity: $subHeaderNumberOpacity3;
	
					i {
						margin-right: 10px;
						font-size: var(--font-size-small);
						border: $subHeaderNumberPhoneBorder3;
						border-radius: $subHeaderNumberPhoneRadius3;
					}
				}
			}
		}

		.header-menu-toggle {
			display: none;
			position: relative;
			right: 0;
			width: 30px;
			height: 25px;
			transition: all 0.5s ease;
	
			@media (max-width: 992px) {
				display: inline-block;
			}
	
			span {
				width: 100%;
				height: 3px;
				background-color: black;
				position: absolute;
				top: 0;
				right: 0;
				left: 0;
				-ms-filter: none;
				-webkit-filter: none;
				filter: none;
				opacity: 1;
				-webkit-transform-origin: center;
				-ms-transform-origin: center;
				transform-origin: center;
				transition: all .25s ease-in-out;
	
				&:nth-child(2){
					top: 10px;
				}
	
				&:nth-child(3){
					top: 20px;
				}
			}
		}
	}

	.main-menu-ctn {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 20px 0 10px;

		@media (max-width: 992px) {
			display: none;
		}

		.main-menu {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding-left: 0;
			list-style-type: none;
			position: relative;
			z-index: 1;
			margin: 0;
			flex-flow: row wrap;
			background: transparent;

			.item-menu {
				position: relative;
				display: inline-block;
				padding-right: $headerLinkPaddingRight2;

				@media (max-width: 1300px) {
					padding-right: 25px;
				}

				@media (max-width: 1200px) {
					padding-right: 15px;
				}

				&:last-child {
					padding-right: 0;
				}

				&.active-child{
					> a {
						background: $headerSubMenuLinkBGHover2;
						color: $headerSubMenuLinkColorHover2;
						opacity: 0.5;
					}
				}

				.secondary-ul {
					background: $headerSubMenuBG2;
					margin: 0px;
					width: 200px;
					padding-left: 0;
					padding-top: 15px;
					padding-bottom: 15px;
					left: 0;
					position: absolute;
					z-index: -1000;
					opacity: 0;
					display: none;

					&:before {
						content: "";
						width: 0;
						height: 0;
						border-left: 10px solid transparent;
						border-right: 10px solid transparent;
						border-bottom: 10px solid $headerSubMenuBG2;
						position: absolute;
						top: -10px;
						left: 15px;
					}
				}

				> a {
					font-size: $headerLinkSize2;
					color: $headerLinkColor2;
					font-weight: $headerLinkWeight2;
					line-height: 66px;
					text-transform: $headerLinkTransform2;
					opacity: 1;
					transition: $transition;

					i {
						margin-left: 10px;
						font-size: 11px;
					}

					@media (max-width: 1200px) {
						font-size: 13px;
					}

					@media (max-width: 1095px) {
						font-size: 11px;
					}

					&.active {
						opacity: 0.5;
					}
				}

				&:hover {
					cursor: pointer;

					> a {
						opacity: 0.5;
					}

					.secondary-ul {
						z-index: 200;
						opacity: 1;
						transition: z-index 0.5s step-start, opacity 0.5s ease;
						display: block;

						li {
							display: block;

							a {
								display: block;
								font-size: $headerSubMenuLinkSize2;
								color: $headerSubMenuLinkColor2;
								font-weight: $headerLinkWeight2;
								line-height: $headerSubMenuLinkLineHeight2;
								text-transform: $headerSubLinkTransform2;
								padding: 10px 30px 10px 30px;

								&.active, &:hover {
									background: $headerSubMenuLinkBGHover2;
									color: $headerSubMenuLinkColorHover2;
									opacity: 0.5;
								}
							}
						}
					}
				}

			}
		}
	}
}

//Opened Menu
body {
	&.-open-menu {
		#header {
			.header-menu-toggle {
				height: 25px;

				span {
					&:first-child {
						transform: rotate(45deg);
						top: 12px;
					}

					&:nth-child(2) {
						opacity: 0;
					}

					&:nth-child(3) {
						transform: rotate(-45deg);
						top: 12px;
					}
				}
			}
		}
	}
}
