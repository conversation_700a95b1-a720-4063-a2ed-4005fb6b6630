/*
	Header Component
*/
$headerHeight4: 100px;
$headerBG4: $white;
$headerBorderBottom4: 1px solid #DDDDDD;
$headerLinkPaddingRight4: 35px;
$headerLinkSize4: 14px;
$headerLinkWeight4: normal;
$headerLinkLineHeight4: 17px;
$headerLinkColor4: #4A4A4A;
$headerLinkTransform4: none;
$headerSubMenuBG4: #eee;
$headerSubMenuLinkBGHover4: transparent;
$headerSubMenuLinkColor4: var(--color-text);
$headerSubMenuLinkColorHover4: var(--color-text);
$headerSubMenuLinkLineHeight4: 20px;
$headerSubMenuLinkSize4: 14px;
$headerSubLinkTransform4: none;

$subHeaderFavoriteBorderLeft4: none;
$subHeaderFavoritePadding4:0 20px 0 0;
$subHeaderFavoriteSize4: 14px;

$subHeaderNumberBG4: #FFFFFF;
$subHeaderNumberBGHover4: #FFFFFF;
$subHeaderNumberBorderLeft4: none;
$subHeaderNumberPadding4: 0 0 0 40px;
$subHeaderNumberColor4: var(--primary-color);
$subHeaderNumberSize4: 18px;
$subHeaderNumberWeight4: 900;
$subHeaderNumberOpacity4: 0.96;
$subHeaderNumberPhoneBorder4: none;
$subHeaderNumberPhoneRadius4: 0px;

/*
	Transparent Header
*/
$transparentSubHeaderLinkColor4: $white;
$transparentSubHeaderNumberColor4: $white;
$transparentHeaderLinkColor4: $white;

/*
	Special Color Header
*/
$specialSubHeaderLinkColor4: blue;
$specialSubHeaderNumberColor4: blue;
$specialHeaderLinkColor4: blue;

#header4 {
	position: relative;
	z-index: 200;
	border-bottom: $headerBorderBottom4;

	.main-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: $headerHeight4;
		padding: 10px 40px 10px;
		background-color: $headerBG4;

		@media (max-width: 1095px) {
			padding: 10px 20px;
		}

		.special-logo {
			fill: $black;
			width: 67px;
      		max-height: 55px;
		}

		.logo-ctn {
			height: 58px;
			display: flex;
			align-items: center;

			> a {
				height: 58px;
				display: flex;
				align-items: center;
			}

			img {
				max-height: 100%;
			}

			.special-logo {
				margin-left: 25px;
			}
		}


		.number-ctn {
			background: $subHeaderNumberBG4;
			padding: $subHeaderNumberPadding4;
			border-left: $subHeaderNumberBorderLeft4;
			transition: $primaryAnimation;
			list-style: none;

			@media (max-width: 1400px) {
				padding: 0 0 0 20px;
			}

			&:hover {
				background: $subHeaderNumberBGHover4;
				color: $primaryButtonTextColorHover;
			}

			a {
				color: $subHeaderNumberColor4;
				font-size: $subHeaderNumberSize4;
				font-weight: $subHeaderNumberWeight4;
				opacity: $subHeaderNumberOpacity4;

				@media (max-width: 1400px) {
					font-size: 15px;
					line-height: 18px;
				}

				i {
					margin-right: 10px;
					font-size: var(--font-size-small);
					border: $subHeaderNumberPhoneBorder4;
					border-radius: $subHeaderNumberPhoneRadius4;
				}
			}
		}

		.main-menu-ctn {
			@media (max-width: 992px) {
				display: none;
			}

			.main-menu {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding-left: 0;
				list-style-type: none;
				position: relative;
				z-index: 1;
				margin: 0;
				flex-flow: row wrap;
				background: transparent;

				.item-menu {
					position: relative;
					display: inline-block;
					padding-right: $headerLinkPaddingRight4;

					@media (max-width: 1350px) {
						padding-right: 25px;
					}

					@media (max-width: 1200px) {
						padding-right: 15px;
					}

					&:last-child {
						padding-right: 0;
					}

					&.active-child{
						> a {
							background: $headerSubMenuLinkBGHover4;
							color: $headerSubMenuLinkColorHover4;
							opacity: 0.5;
						}
					}

					.secondary-ul {
						background: $headerSubMenuBG4;
						margin: 0px;
						width: 200px;
						padding-left: 0;
						padding-top: 15px;
						padding-bottom: 15px;
						left: 0;
						position: absolute;
						z-index: -1000;
						opacity: 0;
						display: none;

						&:before {
							content: "";
							width: 0;
							height: 0;
							border-left: 10px solid transparent;
							border-right: 10px solid transparent;
							border-bottom: 10px solid $headerSubMenuBG4;
							position: absolute;
							top: -10px;
							left: 15px;
						}
					}

					> a {
						// font-family: $headerLinkFont4;
						font-size: $headerLinkSize4;
						color: $headerLinkColor4;
						font-weight: $headerLinkWeight4;
						line-height: 66px;
						text-transform: $headerLinkTransform4;
						opacity: 1;
						transition: $transition;

						i {
							margin-left: 10px;
							font-size: 11px;
						}

						@media (max-width: 1200px) {
							font-size: 13px;
						}

						@media (max-width: 1095px) {
							font-size: 11px;
						}

						&.active {
							opacity: 0.5;
						}
					}

					&:hover {
						cursor: pointer;

						> a {
							opacity: 0.5;
						}

						.secondary-ul {
							z-index: 200;
							opacity: 1;
							transition: z-index 0.5s step-start, opacity 0.5s ease;
							display: block;

							li {
								display: block;

								a {
									display: block;
									font-size: $headerSubMenuLinkSize4;
									color: $headerSubMenuLinkColor4;
									font-weight: $headerLinkWeight4;
									line-height: $headerSubMenuLinkLineHeight4;
									text-transform: $headerSubLinkTransform4;
									padding: 10px 30px 10px 30px;

									&.active, &:hover {
										background: $headerSubMenuLinkBGHover4;
										color: $headerSubMenuLinkColorHover4;
										opacity: 0.5;
									}
								}
							}
						}
					}

					&.-lang {
						margin-left: 5px;
						padding-left: 40px;
						border-left: 1px solid #BBBBBB;
						padding-right: 25px;

						@media (max-width: 1300px) {
							padding-left: 25px;
							padding-right: 15px;
						}
					}

					&.favorite {
						padding: $subHeaderFavoritePadding4;
						border-left: $subHeaderFavoriteBorderLeft4;
			
						a {
							font-size: $subHeaderFavoriteSize4;
							font-weight: 700;
							display: flex;
							align-items: center;
			
							i {
								margin-right: 10px;
								font-size: 20px;
							}
						}
					}
				}
			}

			.special-logo {
				@media (max-width: 1300px) {
					width: 40px;
				}

				@media (max-width: 1225px) {
					display: none;
				}
			}
		}
	}

	.header-menu-toggle {
		display: none;
		position: relative;
		right: 0;
		width: 30px;
		height: 25px;
		transition: all 0.5s ease;

		@media (max-width: 992px) {
			display: inline-block;
		}

		span {
			width: 100%;
			height: 3px;
			background-color: black;
			position: absolute;
			top: 0;
			right: 0;
			left: 0;
			-ms-filter: none;
			-webkit-filter: none;
			filter: none;
			opacity: 1;
			-webkit-transform-origin: center;
			-ms-transform-origin: center;
			transform-origin: center;
			transition: all .25s ease-in-out;

			&:nth-child(2){
				top: 10px;
			}

			&:nth-child(3){
				top: 20px;
			}
		}
	}

	&.transparent {
		.sub-header {
			background: transparent;

			ul {
				li {
					a {
						color: $transparentSubHeaderLinkColor4;
					}
				}
			}

			.number-ctn {
				a {
					color: $transparentSubHeaderNumberColor4;
				}
			}
		}
		.main-header {
			background: transparent;

			.main-menu-ctn {
				.main-menu {
					.item-menu {
						> a {
							color: $transparentHeaderLinkColor4;
						}
					}
				}
			}
		}
	}

	&.special {
		.sub-header {
			background: transparent;

			ul {
				li {
					a {
						color: $specialSubHeaderLinkColor4;
					}
				}
			}

			.number-ctn {
				a {
					color: $specialSubHeaderNumberColor4;
				}
			}
		}
		.main-header {
			background: transparent;

			.main-menu-ctn {
				.main-menu {
					.item-menu {
						> a {
							color: $specialHeaderLinkColor4;
						}
					}
				}
			}
		}
	}
}

//Opened Menu
body {
	&.-open-menu {
		#header {
			.header-menu-toggle {
				height: 25px;

				span {
					&:first-child {
						transform: rotate(45deg);
						top: 12px;
					}

					&:nth-child(2) {
						opacity: 0;
					}

					&:nth-child(3) {
						transform: rotate(-45deg);
						top: 12px;
					}
				}
			}
		}
	}
}
