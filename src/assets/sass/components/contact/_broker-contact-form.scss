.broker-contact-form-cpn {
  position: relative;
  padding: 30px 0 60px;

  &.send {
    display: flex;
    justify-content: center;
    align-items: center;

    .contact-form {
      opacity: 0;
    }

    .contact-form-ctn {
      opacity: 0;
    }

    .form-response {
      opacity: 1;
    }
  }

  form { 
    @include flex(flex-start, space-between);
    gap: 0;
    flex-wrap: wrap;
    &.loading { opacity: 0.4; }
    .form-column { width: 48%; }
  }

  &.narrow form .form-column { width: 100%; }


  .form-loader {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%,-50%);
  }

  .form-response {
    position: absolute;
    top: 70px;
    padding: 0 15px;
    opacity: 0;
    text-align: center;
    transition: all 0.5s ease-in-out;

    &.show { opacity: 1; }

    .main-button { margin: auto; }
  }

	.-form-result {
		.success {
			color: var(--primary-color);
			font-size: 0.9rem;
		}

		.error{
			color: $red;
			font-size: 0.9rem;
		}
	}

  .main-button { margin-right: auto; }
  
  @media (max-width: 991px) {
    form { 
      flex-direction: column;
      .form-column { width: 100%; } 
    }
  }

  @media (max-width: 425px) {
    [formGroupName="phone"] .input-ctn:first-child { margin-bottom: 0; }
  }
}
