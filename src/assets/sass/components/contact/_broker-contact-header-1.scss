.broker-contact-header-1-cpn {

	h1 {
		font-weight: normal;
	}

	.contact-form-ctn {
		@media (min-width: $tablet-lg) {
			padding-right: grid-space(math.div(1,12),0); // todo : do a dynamic class like col-x-xx for this
		}
	}

	.-page-description {
		margin: 0;
		color: var(--color-text);
		font-family: var(--font-secondary);
		font-size: var(--font-size-small);
		line-height: var(--line-height-small);
	}

	.-page-required{
		margin: 25px 0 0;
		color: var(--color-text);
		font-family: var(--font-secondary);
		font-size: 12px;
	}

	.info-box {

		img {
			border-radius: 16px;
		}

		.info-text-ctn {
			padding: 40px 0;

			.title {
				margin-top: 0;
				margin-bottom: 54px;
			}

			.block {
				display: flex;
				margin-bottom: 30px;

				&:last-child { margin-bottom: 0; }

				i {
					font-size: var(--font-size-small);
					color: var(--color-text);
					position: relative;
					top: 4px;
				}

				p, a {
					margin: 0;
					@extend .--normal;
				}

				.text { padding-left: 15px; }
			}

			.location-ctn {

				.text {

					.links { margin-top: 6px; }

					a {
						margin-right: 20px;
						@extend .-cta-small;
					}
				}
			}

			.email-ctn {
				.text {
					@extend .-cta-medium;
				}
			}

			.phone-ctn {
				.text {
					@extend .-cta-medium;
				}
			}
		}
	}

  @media (max-width: 991px) {
    .info-box {    
      .info-text-ctn {
		
        .location-ctn .text .links a { display: block; }
      }
    }
  }
}