.broker-contact-list-cpn {
	padding: 135px 0;
	background-color: var(--color-bg);

	h3 {
    margin: 0;
    margin-bottom: 56px;
  }

	.list-ctn {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 56px 40px;
    margin-bottom: 120px;
	}

  .broker-card-ctn {
    @include flex(flex-start, flex-start);
    gap: 26px;

    .img-ctn {
      width: 100px;
      min-width: 100px;
      height: 100px;
      border-radius: 50%;
      overflow: hidden;
    }

    .text-ctn {
    
      .role {
        margin: 10px 0;
        color: $light-grey;
      }
    
      .phone {
        margin: 0;
        color: var(--color-text);
      }
    }
  }

  .main-button {
    margin: 80px auto 0;
  }

	@media (max-width: 991px) {
    .list-ctn { grid-template-columns: 1fr 1fr; }
	}
  
	@media (max-width: 767px) {
		padding: 45px 0;
    .list-ctn { grid-template-columns: 1fr; }
	}
}
