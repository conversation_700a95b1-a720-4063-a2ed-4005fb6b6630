// Plain full-width CTA with icon

$cta-alert-small-cpn-color: var(--primary-color);
$cta-alert-small-cpn-background: white;

.cta-alert-small-cpn {
	padding-bottom: 120px;
	background-color: $background;

	.container {

		.content-ctn {
			display: flex;
			align-items: center;
			padding: 64px;
			border-radius: 32px;

			@media (max-width: 992px) {
				flex-direction: column;
			}

			@media (max-width: 768px) {
				padding: 40px;
			}

			background: $cta-alert-small-cpn-background;
		}

		.text-ctn {
			color: $cta-alert-small-cpn-color;
			display: flex;
			align-items: center;
			justify-content: space-between;

			@media (max-width: 992px) {
				flex-direction: column;
			}
			
			.title-ctn {
				padding-right: grid-space(math.div(1,12), 1);

				@media (max-width: 992px) {
					padding-right: 0;
					margin-bottom: 40px;
				}

				h2 {
					margin-top: 0;
					margin-bottom: 40px;
				}

				p {
					margin-top: 0;
					margin-bottom: 0;
				}
			}

			.button-ctn a {
				text-transform: none;
			}
		}
	}
}
