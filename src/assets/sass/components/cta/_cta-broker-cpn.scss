/*
	CTA Broker Component
*/

$ctaBrokerPadding: 80px 0;

$ctaBrokerObliqueBG: #F6F6F6;
$ctaBrokerObliqueWidth: 240px;
$ctaBrokerObliqueRight: 290px;
$ctaBrokerObliqueSkew: skew(-45deg);

$ctaBrokerTagSize: 13px;
$ctaBrokerTagLineHeight: 18px;
$ctaBrokerTagNameWeight: 700;

.cta-broker-cpn {
	overflow: hidden;
	padding-top: 120px;
	padding-bottom: 120px;
	position: relative;

	@media (max-width: 992px) {
		padding-top: 60px;
		padding-bottom: 60px;
	}

	.list {
		padding-left: 0;
	}

	.cta-broker-content {
		// padding: $ctaBrokerPadding;
		display: flex;
		flex-direction: column;
		justify-content: center;
		padding-left: grid-space(math.div(1,12), 0);

		@media (max-width: 992px) {
			padding-left: 0;
		}

		.-eyebrow {
			color: var(--secondary-color);
		}

		.title {
			margin-top: 32px;
			margin-bottom: 0;
			font-weight: normal;
		}

		.subtext {
			margin-top: 28px;
			margin-bottom: 0;

			@media (max-width: 992px) {
				margin-top: 20px;
			}
		}
		
		.main-button {
			margin-top: 20px;
		}
	}

	.oblique {
		display: block;
		background: $ctaBrokerObliqueBG;
		height: 100%;
		width: $ctaBrokerObliqueWidth;
		position: absolute;
		z-index: 1;
		bottom: 0%;
		right: $ctaBrokerObliqueRight;
		transform: $ctaBrokerObliqueSkew;
	}

	.cta-broker-img {
		text-align: center;
		position: relative;

		img{
			border-radius: 32px;

		@media (max-width: 992px) {
			margin-bottom: 20px;
		}
		}
	}

	.broker-tag {
		min-width: 280px;
		padding: 12px 20px;
		position: absolute;
		right: 20px;
		bottom: 60px;
		background-color: rgba(0,0,0,0.5);
		text-align: left;

		@media (max-width: 992px) {
			right: 0;
		}

		p {
			margin: 0;
			color: $white;
			font-family: var(--font-secondary);
			font-size: $ctaBrokerTagSize;
			line-height: $ctaBrokerTagLineHeight;
		}

		.name {
			font-weight: $ctaBrokerTagNameWeight;
		}
		.role {
			font-weight: normal;
		}
	}
}

.cta-broker-cpn.-home-hero-type {
	
	@media (max-width: 992px) {
		padding-top: 0;
		background: white;
	}

	.cta-broker-content {
		@media (max-width: 992px) {
			padding-top: 34px;
			padding-bottom: 60px;
		}
	}

	.background-ctn {
		position: relative;
		display: none;

		@media (max-width: 992px) {
			display: block;
		}
	}

	.cta-broker-img {
		@media (max-width: 992px) {
			display: none;
		}
	}

	.broker-tag {
		@media (max-width: 992px) {
			bottom: 15px;
			min-width: 220px;
		}
	}
}
