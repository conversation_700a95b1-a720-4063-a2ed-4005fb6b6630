.pdf-guide-cta-cpn {
    margin: 0;
    padding-top: 135px;

    &.full-width {
        background-color: $white;
        margin: 0;
    }

    .container {

        .grid {
            border-radius: 40px;
            border: 2px solid $secondary-color;
            padding: 64px 72px;

            @media (max-width: 992px) {
                padding: 32px 20px;
            }
        }

        .img-ctn {
            @include flex();

            img {
                width: 100%;
            }
        }
        
        .text-ctn {
            width: 100%;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            padding-left: grid-space(math.div(0.5,12), 1);
        
            p.description {
                margin-bottom: 30px;
            }

            .button-ctn {
                margin-right: auto;
            }
        }
    }

	@media (max-width: 767px) {

        margin: 60px 0;

		.container {
			
			.img-ctn {
                width: auto;
                margin: 30px 0 40px;
                margin-bottom: 0;
			}

			.text-ctn {
                text-align: center;

                h2 {
                    margin-top: 0;
                }

				a.main-button {
					margin: auto;
				}
			}
		}
	}
}
