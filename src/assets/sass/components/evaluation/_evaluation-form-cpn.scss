/*
	Evaluation Form Components
*/

.evaluation-form-cpn {
	padding: 80px 0;
	background: url('/assets/images/turmel/backgrounds/bg_estimation.webp') no-repeat center center;
	background-size: cover;
  position: relative;

  .block {
    display: block;
  }

  .-emphase-title {
    margin: 40px 0;
  }

  .container {

    &.send {
      display: flex;
      justify-content: center;
      align-items: center;

      .alert-form {
        opacity: 0;
      }
    }

    @media (max-width: 767px) {

      &.send {
        .alert-form {
          display: none;
        }
      }
    }
  }

  .map-ctn {
    margin-top: 40px;

    .info-map-ctn {
      padding: 24px;
      background-color: $background;

      p {
        margin: 0;
      }

      .found {
        margin-bottom: 5px;
      }

      .address {

        span {
          margin-left: 20px;
          font-weight: 600;
          cursor: pointer;

          @media (max-width: 767px) {
            margin-left: 0;
          }
        }
      }
    }
  }

  #street-view {
    height: 360px;
    position: relative;

    .no-view {
      background-color: #fbfbfb;
      position: absolute;
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      transition: $primaryAnimation;
    }

    .no-results {
      background-color: #fbfbfb;
      position: absolute;
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      opacity: 0;
      transition: $primaryAnimation;

      &.show {
        opacity: 1;
        z-index: 12;
      }

      &-inner {
        text-align: center;
        padding: 20px;
        color: var(--color-text);
      }
    }
  }

  .alert-form {
    padding: 80px 100px;
    border-radius: 24px;
    background-color: $white;

    @media (max-width: 767px) {
      padding: 60px 20px;
    }

    .custom-radio-group {
			width: 50%;
			float: left;

			@media (max-width: 767px) {
				float: none;
				width: 100%;
			}

			input{
				display: none;
			}

			label {
				color: $black;
			}
		}
  }

  .loading-inner {
    opacity: 0.5;
  }

  .required-input {
    color: #e71624;
  }

  .form-loader {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }

  .form-head {
		position: relative;
	}

  .step-ctn {
    display: none;
    &.show {
      display: block;
    }
  }

  .-page-description {
    margin-top: 20px;
    margin-bottom: 0;
    padding-bottom: 40px;
  }

  .input-ctn {
    padding-top: 24px;

    @media (max-width: 767px) {
      padding-top: 20px;
    }
  }

  #file-size-limit {
    color: var(--color-text);
    font-family: var(--font-secondary);
    font-size: 12px;
    line-height: var(--line-height-cta-small);
  }

  .upload-label-wrapper {
    position: relative;
    #label-file-upload {
      background-color: var(--primary-color);
      color: $white;
      padding: 6px 21px;
      max-width: 100%;
    }

    #file-upload-error {
      position: absolute;
      bottom: -50px;
      color: $red;
      font-size: var(--font-size-cta-small);
    }

    .file-list {
      display: flex;
      flex-direction: column;
      gap: 5px;
      margin-top: 10px;
      .file-item {
        font-size: var(--font-size-cta-small);
        color: var(--color-text);
        span {
          font-size: 12px;
          margin-right: 5px;
          &:hover {
            cursor: pointer;
          }
        }
      }
    }
  }

  .warning-message {
    margin: 35px auto 0;
    color: var(--color-text);
    font-family: var(--font-primary);
    font-size: var(--font-size-cta-small);
    line-height: var(--line-height-cta-small);
    text-align: right;
  }

  .-page-required {
    color: var(--color-text);
    font-family: var(--font-primary);
    font-size: 12px;
    line-height: var(--line-height-cta-small);
    text-align: right;
  }

  .button-ctn {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding-top: 40px;

    @media(max-width: 968px){
      flex-direction: column;
      align-items: flex-start;
      gap: 40px;

      .-previous{
        margin-bottom:20px;
      }
    }
  }

  .form-response {
    position: absolute;
    text-align: center;
    opacity: 0;
		padding: 64px;
		background: $white;
		border-radius: 24px;
		max-width: 800px;
		margin: 0 auto;
    top: 50px;

		@media (max-width: 768px) {
			padding: 60px 20px;
		}

    .button-ctn {
      justify-content: center;
    }

    &.show {
      transition: all 0.5s ease-in-out;
      opacity: 1;
    }

    @media (max-width: 767px) {
      padding: 30px;

      &.show {
        position: relative;
      }
    }
  }

  .main-button.disabled {
    background-color: $light-grey;
    cursor: not-allowed;
  }

  .too-far {
    display: flex;
    align-items: flex-start;
    flex-direction: row-reverse;
    gap: 20px;
    padding: 0 30px 0 0;

    label {
      color: var(--color-text);
      font-family: var(--font-secondary);
      font-size: var(--font-size-small);
      line-height: 24px;
    }

    &-alert {
      background-color: $red;
      color: white;
      padding: 10px 20px;
      border-radius: 5px;
      margin-bottom: 50px;

      .description {
        font-size: 14px;
        line-height: 20px;
      }
    }
  }
}
