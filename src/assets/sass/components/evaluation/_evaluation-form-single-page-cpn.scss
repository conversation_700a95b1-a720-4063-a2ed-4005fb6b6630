/*
	Evaluation Form Single Page Components
*/

.evaluation-form-single-page-cpn {
	padding-top: 60px;
	padding-bottom: 80px;
	background-color: var(--color-bg);

	.block {
		display: block;
	}

	.container {

		&.send {
			display: flex;
			justify-content: center;
			align-items: center;
			height: 600px;

			.alert-form{
				display: none;
			}
		}

		@media(max-width: 767px){
			margin: 0 15px;

			&.send {
				.alert-form{
					display: none;
				}
			}
		}

	}

	.map-ctn {
		border: 1px solid #CCCCCC;
		margin-bottom: 55px;
		margin-top: 40px;

		.info-map-ctn {
			padding: 30px;

			p {
				margin: 0;
			}

			.found {
				margin-bottom: 20px;
				color: $black;
			}

			.address {
				color: var(--color-text);
				font-size: var(--font-size-small);
				line-height: 19px;

				span {
					margin-left: 20px;
					color: var(--primary-color);
					font-family: var(--font-secondary);
					font-size: 13px;
					font-weight: 600;
					line-height: 18px;
					cursor: pointer;

					@media (max-width: 767px) {
						margin-left: 0;
					}
				}
			}
		}
	}

	#street-view {
		height: 360px;
		position: relative;

		.no-view{
			background-color: #fbfbfb;
			position: absolute;
			width: 100%;
			height: 100%;
			display: flex;
			justify-content: center;
			align-items: center;
			transition: $primaryAnimation;
    }

		.no-results{
			background-color: #fbfbfb;
			position: absolute;
			width: 100%;
			height: 100%;
			display: flex;
			justify-content: center;
			align-items: center;
			opacity: 0;
			transition: $primaryAnimation;

			&.show{
				opacity: 1;
				z-index: 12;
			}

			&-inner{
				text-align: center;
				padding: 20px;
				color: var(--color-text);
			}
		}
	}

	.alert-form {
		padding: 80px 100px;
		background: $white;
		border-radius: 24px;

		@media (max-width: 767px) {
			padding: 60px 0px;
		}
	}

	.loading-inner{
		opacity: 0.5;
	}

	.required-input {
		color: #E71624;
	}

	.form-loader{
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%,-50%);
	}

	.form-head {
		position: relative;
		padding-bottom: 40px;
	}

	// .-page-title {
	// 	margin: 0;
	// 	font-size: $h2TitleSize;
	// 	line-height: $h2TitleLineHeight;

	// 	@media (max-width: 450px) {
	// 	text-align: center;
	// 	}
	// }

	.-page-description {
		margin-top: 20px;
		margin-bottom: 0;
		padding-bottom: 40px;
		color: var(--color-text);
		font-family: var(--font-secondary);
		// font-size: $primaryTextSize;
		// line-height: $primaryTextLineHeight;
	}

	// .emphase-title {
	// 	margin-bottom: 0;
    //     margin-top: 60px;
	// }

	.input-ctn {
		padding-top: 40px;
		.img-weight-warning{
			color: var(--color-text);
			font-size: 0.675rem;
			font-style: italic;
			margin-top: -10px;
		}
	}

	.warning-message {
		margin: 35px auto 0;
		color: var(--color-text);
		font-family: var(--font-secondary);
		font-size: var(--font-size-cta-small);
		line-height: var(--line-height-cta-small);
		text-align: right;
	}

	.-page-required{
		color: var(--color-text);
		font-family: var(--font-secondary);
		font-size: 12px;
		line-height: var(--line-height-cta-small);
		text-align: right;
	}

	.button-ctn {
		display: flex;
		align-items: center;
		justify-content: flex-end;
		padding-top: 40px;
		
		@media(max-width: 500px){
			flex-direction: column;
			align-items: flex-start;

		}
	}

	.form-response {
		position: absolute;
		text-align: center;
		opacity: 0;
    	padding: 0 15px;
		.button-ctn {
			justify-content: center;
		}

		&.show{
			transition: all 0.5s ease-in-out;
			opacity: 1;
		}

		@media(max-width: 767px){
			padding: 30px;

			&.show{
				position: relative;
			}
		}
	}
}
