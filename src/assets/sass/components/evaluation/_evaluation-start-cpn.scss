/*
	Evaluation Start Components
*/

.evaluation-start-cpn {
	padding: 80px 0;
	background: url('/assets/images/turmel/backgrounds/bg_estimation.webp') no-repeat center center;
	background-size: cover;

	@media (max-width: 992px) {
		padding: 80px 20px;
	}

	.inner {
		padding: 64px;
		background: $white;
		border-radius: 24px;
		max-width: 800px;
		margin: 0 auto;

		@media (max-width: 768px) {
			padding: 60px 20px;
		}
	}

	.-page-description {
		margin-top: 20px;
		margin-bottom: 0;
		padding-bottom: 30px;
		color: var(--color-text);
		font-family: var(--font-secondary);
		font-size: var(--font-size-small);
		line-height: var(--line-height-small);
		text-align: center;
	}

	.main-button {
		margin: 40px auto;
	}

  // Pin icon
  .input-ctn{
    position: relative;
    &::after {
      font-family: 'icomoon' !important;
      content: '\e91a';
      display: block;
      position: absolute;
      top: 16px;
      right: 32px;
      font-size: 24px;
      color: $black;

		@media (max-width: 768px) {
			display: none;
		}
    }
  }

  @media(max-width: 425px) {
    .main-button {
      width: 100%;
    }  
  }
}