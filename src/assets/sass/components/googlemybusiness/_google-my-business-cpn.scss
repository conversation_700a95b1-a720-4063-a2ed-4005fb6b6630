.reviews-slider-cpn {
	background: url("/assets/images/turmel/backgrounds/bg_texture.webp");
	padding:          160px 0 90px;

	@media (max-width: 992px) {
		padding: 50px 0 100px;

		.text img {
			display: none;
		}

		h3 {
			margin: 0;
		}
	}

	.container { position: relative; }

	.text {
		padding-right: grid-space(1/12, 1);
		.undertitle {
			@extend .-eyebrow;
			margin: 0;
			margin-bottom: 40px;
			color: $secondary-color;
		}

		.title {
			margin: 0;
			color: var(--color-title);
		}
	}

	.reviews-list-ctn {
		overflow: hidden;
	}

	.review {
		padding-left: 60px;

		@media (max-width: 992px) {
			padding-left: 0;
			border-left: none;
		}

		&-header {
			display: flex;
			align-items: center;
			margin-bottom: 20px;
			gap: 32px;
			margin-top: 32px;

			&-left {
				display: flex;
				align-items: center;

				.reviewer-image {
					width: 80px;
					height: 80px;
					border-radius: 100px;
				}
			}

			&-right {
				.reviewer-name {
					color: var(--color-text);
					font-size: 18px;
					font-weight: 700;
					line-height: 50px;
					margin-bottom: 0;
				}

				.reviewer-rating {
					display: flex;
					align-items: center;
					margin-bottom: 8px;

					.rating-stars {
						display: flex;

						.icon-star {
							color: #ccc;
							margin-right: 2px;

							&.checked::before {
								color: #B5A068; 
							}
						}
					}

					.rating-value {
						font-size: 18px;
					}
				}

				.reviewer-date {
					color: #777;
					font-size: 13px;
					font-style: normal;
					font-weight: 400;
					line-height: 19.6px;
				}
			}
		}

		&-body {

			.review-text {
				color: var(--color-text);
				@extend .--large;
			}
		}
	}

	.pagination {
		display: flex;
		justify-content: flex-end;
		gap: 15px;
		gap: 40px;
		margin-top: 55px;

		@media (max-width: 992px) {
			display: none;
		}
	}

	.swiper-btn {
		background: none;
		width: 18px;
		// height: 30px;
		// transform: translate(0%, 50%);
		margin-top: 0;
		top: unset;

		border-radius: 100%;
		background-color: var(--primary-color);
		width: 50px;
		height: 50px;
		position: relative;

		&:before {
			position: absolute;
			font-family: 'icomoon' !important;
			speak: none;
			font-style: normal;
			font-weight: normal;
			font-variant: normal;
			text-transform: none;
			line-height: 1;
			font-size: 20px;
			color: $white;
			left: 50%;
			top: 50%;
			transform: translate(-50%, -50%);
		}

		@media (max-width: 992px) {
			display: none;
		}

		&.swiper-button-prev {
			// left: -30px;

			&:before {
				content: "\e909";
			}
		}

		&.swiper-button-next {
			// right: -30px;

			&:before {
				content: "\e90a";
			}
		}
	}
}