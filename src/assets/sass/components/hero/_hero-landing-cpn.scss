.hero-landing-cpn {
    position: relative;
    height: 100vh;

    &.-mobile {
        display: none;
    }

    @media (max-width: $tablet-sm) {
        height: 50vh;
    }

    &:before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(0deg, rgba(0, 0, 0, 0.00) 56.73%, rgba(0, 0, 0, 0.40) 100%), linear-gradient(0deg, rgba(0, 0, 0, 0.30) 0%, rgba(0, 0, 0, 0.30) 100%);
        z-index: 1;
    }

    &.hero-landing-home {

        @media (max-width: $tablet-sm) {
            height: auto;
                
            &.-mobile {
                display: block;

                .container {
                    position: unset;
                }
            }

            &.-desktop {
                height: auto;

                .container {
                    display: none;
                }
            }
        }

        &:before {
            display: none;
        }

        .container {
            @media (max-width: $tablet-sm) {
                background-image: url('/assets/images/turmel/backgrounds/bg_texture.webp');
                background-size: cover; 
                background-position: center;
                background-repeat: no-repeat;
            }
        
            .title {

                @media (max-width: $tablet-sm) {
                    color: $black;
                }
            }
        }

        img.background {
            @media (max-width: $tablet-sm) {
                &.-mobile {
                    display: block;
                }

                &.-desktop {
                    display: none;
                }
            }
        }
    }

    img.background {
        width: 100%;
        height: 100%;
        min-height: 200px;
        object-fit: cover;
        
        &.-mobile {
            display: none;
        }

        &.-desktop {
            display: block;
        }
    }

    .container {
        @include flex(flex-end, flex-start);
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        padding: 0;
        z-index: 2;

        .text-ctn {
            padding-left: 48px;
            padding-bottom: 56px;
            width: 70%;

            @media (max-width: $tablet) {
                width: 100%;
            }

            @media (max-width: $tablet-sm) {
                padding: 34px 20px;
            }
        }
        
        .title {
            color: $white;
            margin: 0;
            font-size: 80px;
            font-weight: 400;
            line-height: 83px;
            letter-spacing: -0.81px;

            @media (max-width: $tablet-lg) {
                font-size: 50px;
                line-height: 53px;
            }

            @media (max-width: $tablet-sm) {
                font-size: 32px;
                line-height: 36px;
                letter-spacing: -0.32px;
            }
        }
    }

    .button-ctn {
        display: flex;
        gap: 16px;
        margin-top: 50px;

        @media (max-width: $tablet-sm) {
            gap: 8px;
            flex-wrap: wrap;
            margin-top: 32px;

            .main-button {
                height: 42px;
            }
        }
    }
}