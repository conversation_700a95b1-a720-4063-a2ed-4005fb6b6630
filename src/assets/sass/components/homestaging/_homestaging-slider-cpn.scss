.homestaging-slider-cpn {
    background: white;
    padding: 130px 0;

    .subtitle {
        color: var(--secondary-color);
    }

    .title {
        margin-top: 32px;
        margin-bottom: 56px;
    }

    .description {
        margin: 0;
    }

    .container {
        .slider-ctn {
            // width: 500px;
            margin-left: 80px;

            @media (max-width: 992px) {
                margin-left: 0;
                margin-top: 0;
            }
        }

        .slider-default-cpn {
            @media (max-width: 992px) {
                margin-top: 0;
            }
        }
    }

    .team-info-box {
        margin-top: 130px;
        padding: 64px;
        background-color: $background;
        border-radius: 32px;
        border: none;

        &-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 32px;

            .title {
                margin-top: 0;
            }

            .description {
                font-family: var(--font-primary);
                @extend .--normal;
            }
        }

        @media (max-width: 992px) {
            padding: 40px;
        }
    }

    @media (max-width: 992px) {
        padding: 40px 0;

        .container {
            .row {
                display: block;

                .text-ctn {
                    padding: 20px;
                    width: auto;
                }

                .slider-ctn {
                    margin: auto;
                }
            }
        }

        .slider-default-cpn {
            margin-top: 40px;
        }

        .team-info-box {
            margin-bottom: 60px;
        }
    }

    @media (max-width: 500px) {
        .container {
            .row {
                .slider-ctn {
                    width: 310px;
                }
            }
        }

    }
}