.landing-footer-cpn {
  padding-top: 120px;
  padding-bottom: 80px;
  background-color: $black;

  @media (max-width: 767px) {
    padding-top: 60px;
    padding-bottom: 40px;
  }
  
  .main-button.-primary {
    min-width: auto;
    font-size: var(--font-size-cta-small);
    line-height: var(--line-height-cta-small);
    text-transform: none;
  }

  .footer-info {
    display: flex;
    align-items: center;
    gap: 120px;
    max-width: 770px;
    margin: auto;

    @media (max-width: 767px) {
      flex-direction: column;
      align-items: center;
      gap: 60px;
      max-width: 100%;
    }

    > div { 
      width: 50%;
      &:first-child { margin-left: auto; }
      &:last-child { margin-right: auto; }

      @media (max-width: 767px) {
        width: 100%;
        margin-left: 0;
        margin-right: 0;
        text-align: center;
      }
    }

    .logo-ctn {
      p {
        font-size: 12px;
        line-height: 17px;
      }
    }

    .contact-ctn {
      .location-ctn,
      .mail-ctn,
      .phone-ctn {
        width: auto;
        display: flex;
        align-items: flex-start;
        margin-bottom: 20px;

        a, p {
          color: $white;
          font-size: 16px;
          line-height: normal;
          font-weight: 400;
        }

        .icon {
          position: relative;
          top: 2px;
          padding-right: 15px;
          color: $white;

          &-pin {
            left: -3px;
            padding-right: 9px;
            font-size: 23px;
          }
        }
      }

      .phone-ctn a {
        font-size: 16px;
        line-height: 21px;
        font-weight: 700;
      }
    }
  }

  .footer-share {
    text-align: center;

    .main-button { margin: 75px auto; }

    p {
      margin-bottom: 30px;
      color: $white;
      font-size: 16px;
      line-height: 22px;
    }

    .share-ctn {
      display: flex;
      gap: 20px;
      width: max-content;
      margin: auto;

      a {
        color: $white;
        font-size: 18px;
      }
    }
  }
}

