.landing-header-cpn {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 120px;
  padding: 0 40px;

  @media (max-width: 540px) {
    flex-direction: column;
    align-items: center;
    gap: 20px;
    height: auto;
    padding: 20px 15px;
  }

  .logo-ctn {
    display: flex;
    align-items: center;
    gap: 40px;

    .special-logo {
      width: 120px;
      height: auto;

      @media (max-width: 767px) {
        display: none;
      }
    }

    a { display: block; }
  }

  .main-button {
    min-width: auto;
    font-size: var(--font-size-cta-medium);
    line-height: var(--line-height-cta-medium);
    text-transform: none;
  }

  a {
    color: var(--color-text);
    transition: $primaryAnimation;
    cursor: pointer;
    &:hover { color: var(--primary-color); }
  }
  
  nav {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 30px;
    height: 100%;
  }

  @media (max-width: 767px) {
    .main-button { 
      padding: 10px 22px;
    }
  }

  @media (max-width: 570px) {
    nav {
      gap: 10px;
    }

    .main-button {
      height: auto;
      margin-left: 10px;
      font-size: 11px;
    }
    
    .language-switcher { 
      padding-left: 20px;
    }
  }
}