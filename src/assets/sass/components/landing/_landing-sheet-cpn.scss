.landing-sheet-cpn {

  .laraberg {
    h1 {
      @extend .-small;
    }

    h2 {
      @extend .-small;
    }

    h3 {
      @extend .-small;
    }

    h4 {
      @extend .-small;
    }

  }

  .article-share-cpn {
    position: sticky;
    height: 0;
    top: 40vh;
    margin-left: -80px;

    div {
      display: flex;
      padding-top: 20px;
      align-items: baseline;
      flex-direction: column;

      a, button {
        margin-bottom: 20px;
        margin-right: 0;
      }

      .fa, i, button::before {
        font-size: 20px;
        color: var(--color-text);
      }
    
    }
  }

  .main-img-ctn {
    text-align: center;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      max-height: 700px;
      background-position: center;
    }
  }

  .main-text-ctn {
    padding-top: 50px;
    margin-bottom: 96px;

    @media (max-width: 767px) {
      margin-bottom: 40px;
      padding-top: 30px;
    }

    ul {
      padding-left: 0px;
      padding-bottom: 30px;

      li {
        position: relative;
        padding: 16px 0px 16px 40px;
        // color: $listColor;
        @extend .--normal;

        &:before {
          color: var(--primary-color);
          font-family: 'icomoon';
          speak: none;
          font-style: normal;
          font-weight: normal;
          font-variant: normal;
          text-transform: none;
          line-height: 1;
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
          content: "\e90d";
          position: absolute;
          left: 0;
          top: 22px;
        }

        p {
          margin: 0;
        }
      }
    }

    .embeddedContent {
      margin: 40px 0;
      position: relative;
      padding-bottom: 56.25%;
      /* 16:9 */
      height: 0;

      iframe {
        position: absolute;
        top: 0;
        left: 0;
        width: 100% !important;
        height: 100% !important;
      }
    }
  }

  .cta-ctn {
    border-radius: 40px;
    background: $background;
    padding: 72px;

    @media (max-width: 767px) {
      padding: 40px;
    }

    @media (max-width: 540px) {
      padding: 40px 20px;
    }

    h2 {
      margin: 0;
      margin-bottom: 56px;
      text-align: center;

      p {
        @extend .-h4;
        margin: 0;
      }
    }

    .main-button { margin: auto; }
  }

  @media (max-width: 767px) {
    h1 {
      font-size: 30px;
      line-height: 38px;
    }

    .article-share-cpn { display: none; }

    .main-text-ctn {
      padding-top: 30px;
      padding-bottom: 30px;
      font-size: 14px;
      line-height: 26px;

      h2 {
        font-size: 24px;
        line-height: 30px;
      }
    }
  }
}