.neighborhood-demographics-cpn {
	background: var(--color-bg);

	h3 {
		margin-bottom: 64px;
	}

	.chart-wrap {
		display: flex;
		flex-flow: row wrap;

		.block {
			width: calc(100% * 1/2 - 20px);
			margin-right: 40px;
			margin-bottom: 40px;
			border-radius: 24px;

			@media (max-width: 767px) {
				margin-right: 0px;
				width: 100%;

				&:nth-child(4n) {
					margin-bottom: 0px;
				}
			}

			&:nth-child(2n) {
				margin-right: 0px;
			}
		}
	}

	.chart-wrap {
		// max-width: 1070px;
		margin: 0 auto;
		@include clearfix();
	}

	.chart-ctn {
		text-align: center;
		background: white;
		padding: 80px 40px 60px 40px;

		@media (max-width: 992px) {
			padding: 60px 20px 40px 20px;
		}

		.label {
			padding-top: 20px;
			color: var(--color-text);
			@extend .--small;
		}
	}

	.stats-ctn {
		display: flex;
		align-items: center;
		justify-content: center;
		background: white;

		@media (max-width: 767px) {
			display: block;
			padding: 20px 30px;
		}
	}

	.stats {
		display: flex;
		align-items: center;
		justify-content: space-between;
		min-width: 360px;
		border-bottom: 1px solid #CBC7B5;

		&:last-child {
			border: none;
		}

		@media (max-width: 992px) {
			max-width: none;
			min-width: 0px;
		}

		p {
			margin: 40px 0;
		}

		.name {
			color: $black;
			@extend .--small;
		}

		.value {
			color: $black;
			@extend .--large;
			text-align: right;
		}
	}
}