$nbhItemBackground: $white;
$nbhItemShadow: 0 2px 4px 0 rgba(0,0,0,0.1);

.neighborhood-grid-cpn {
	background-color: var(--color-bg);
	padding-bottom: 80px;
  
	.page-head {
	  max-width: 80%;
	  margin: 0 auto 80px;
	  text-align: center; 

	  .title {
		margin-bottom: 80px;

		@media (max-width: 991px) {
			margin-bottom: 40px;
		}
	  }
  
	  .text { 
		max-width: 870px;
		margin: 0 auto;
	  }
	}
  
	.nbh-grid {
	  display: grid;
	  grid-template-columns: 1fr 1fr 1fr;
	  gap: 100px 30px;
	}
  
	.nbh-item {
		@include flex(flex-start, space-between, column);
		gap: 0;
		
	  .item-img {
		position: relative;
		aspect-ratio: 1/1;
		overflow: hidden;
		border-radius: 16px;
  
		img { 
			@include img();
			border-radius: 16px;
			transition: 0.4s all ease;
		}
  
		// Darker overlay
		&::after {
			@include pseudoElement();
			top: 0;
			width: 100%;
			height: 100%;
			background-color: black;
			opacity: 0;
			transition: 0.4s all ease;
			pointer-events: none; 
			border-radius: 16px;
		}
	  }
  
	  .item-title {
		font-family: var(--font-primary);
		font-size: 34px;
		font-weight: 500;
		line-height: 42px;
		letter-spacing: -0.34px;
		margin: 32px 0;
		text-align: center;
	  }

	  p {
		margin: 0;
		margin-bottom: 32px;
		@extend .--small; 
		text-align: center;
	  }
  
	  .small-link { 
		margin: auto;

		.icon-arrow-right {
			margin-left: 6px;
			transition: $primaryAnimation;
		}

		&:hover {
			.icon-arrow-right {
				transform: translateX(10px);
			}
		}
	  }
  
	  &:hover {
		.item-img {
		  img { transform: scale(1.06); }
		  &::after { opacity: 0.2; }
		  cursor: pointer;
		}
	  }
	}
  
	.loading { padding: 60px 0 100px; }

	&.-vertical{
		.nbh-grid {
			grid-template-columns: 1fr;
			gap: 60px;
		}
		
		.nbh-item {
			@include flex(flex-start, space-between, row);
			box-shadow: $nbhItemShadow;
			background-color: $nbhItemBackground;
			min-height: 400px;
			
			.item-img {
				width: 40%;
				height: 100%;
			}

			.content{
				width: 60%;
				padding: 40px 80px;
				

				.item-title{
					margin-top: 0;
				}

				.main-button{
					width: min-content;
					margin-top: 40px;
				}
			}
		}
	
	}

	@media (max-width: 991px) {
	  .page-head {
		max-width: none;
		// .title { @include fontSize(60px, 66px); }
	  }
  
	  .nbh-grid {
		grid-template-columns: 1fr 1fr;
	  }

	  &.-vertical{
		.nbh-item {
			flex-direction: column;

			.item-img {
				min-height: 250px;
				width: 100%;
			}

			.content{
				padding: 40px;
				width: 100%;
			}
		}
	  }
	}
  
	@media (max-width: 600px) {
	  .page-head {
		// .title { 
		//   margin: 70px 0 40px;
		//   @include fontSize(44px, 50px);
		// }
	  }

	  .nbh-item {
		.main-button { width: 100%; }
	  }
  
	  .nbh-grid {
		grid-template-columns: 1fr;
	  }

	  &.-vertical{
		.nbh-item {
			.content{
				padding: 30px;

				.main-button{
					width: auto;
				}
			}
		}
	  }
	}
  }

  .neighborhoods-custom-cpn {
	background-color: $background;

	.container {
		display: grid;
		grid-template-columns: 1fr 1fr;
		align-items: center;

		@media (max-width: 991px) {
			grid-template-columns: 1fr;
		}
	}

	.map {
		height: 100%;

		@media (max-width: 991px) {
			height: auto;
		}

		img {
			width: 100%;
			height: 100%;
			object-fit: cover;
			border-radius: 32px;

			@media (max-width: 991px) {
				max-height: 450px;
			}
		}
	}

	.content {
		padding-left: grid-space(math.div(1,12), 1);

		@media (max-width: 991px) {
			padding-left: 0;
			margin-top: 40px;
		}

		.subtitle {
			@extend .-thumbnail-meta;
			color: var(--secondary-color);
		}

		.title {
			margin-top: 32px;
			margin-bottom: 56px;
			@extend .-h2;
		}

		.text {
			margin-top: 0;
			margin-bottom: 40px;
		}

		.cta-cpn {
			@include flex(center, flex-start, row);
			gap: 8px;

			@media (max-width: 540px) {
				flex-direction: column;
				gap: 16px;
			}

			.main-button {
				min-width: fit-content;
			}
		}
	}
  }