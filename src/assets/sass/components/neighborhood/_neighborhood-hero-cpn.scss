.neighborhood-hero-cpn {
    position: relative;
	height: 760px;

    @media (max-width: 767px) {
        height: 400px;
    }

    &:before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(0deg, rgba(0, 0, 0, 0.00) 56.73%, rgba(0, 0, 0, 0.40) 100%), linear-gradient(0deg, rgba(0, 0, 0, 0.30) 0%, rgba(0, 0, 0, 0.30) 100%);
        z-index: 1;
    }

    &.hero-landing-home {
        &:before {
            display: none;
        }
    }

    img.background {
        width: 100%;
        height: 100%;
        min-height: 200px;
        object-fit: cover;
    }

    .container {
        @include flex(flex-end, flex-start);
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        padding: 0;
        z-index: 2;

        .text-ctn {
            padding-left: 48px;
            padding-bottom: 56px;

            @media (max-width: 767px) {
                padding-left: 20px;
                padding-bottom: 24px;
            }
        }
        
        .title {
            color: $white;
            margin: 0;
            font-size: 80px;
            font-weight: 400;
            line-height: 83px;
            letter-spacing: -0.81px;

            @media (max-width: 767px) {
                font-size: 42px;
                line-height: 42px;
            }
        }
    }

    .button-ctn {
        display: flex;
        gap: 16px;
        margin-top: 50px;

        @media (max-width: $tablet-sm) {
            flex-direction: column;
            gap: 16px;
        }
    }
}