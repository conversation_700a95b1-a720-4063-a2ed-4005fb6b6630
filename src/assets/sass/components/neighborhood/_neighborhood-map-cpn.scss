.neighborhood-map-cpn {
	position: relative;

	&.style-2 {
		#map {
			height: 600px;
			position: relative;
		}
	}	
	#map {
		position: fixed;
		width: 100%;
		min-height: 600px;
		height: 100%;
		right: 0;
		top: 0;
		transition: $primaryAnimation;

		.mapboxgl-ctrl-top-right {
			top: 160px;
		}
	}

	.main-button{
		max-width:100%;
	}

	.mapbox-logo, .map-legends {
		display: none;
	}

	#currentPopup-ctn {
		@media (max-width: 767px){
			.background {
				position: fixed;
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;
				background: rgba(0,0,0,0.5);
			}
		}
	}

	#currentPopup {
		position: absolute;
		top: 160px;
		right: 60px;
		background: white;
		box-shadow: 0 4px 8px 0 rgba(0,0,0,0.1);
		max-width: 320px;
		min-width: 300px;

		@media (max-width: 767px){
			top: 34%;
			right: inherit;
			left: 50%;
			transform: translate(-50%,-50%);
		}

		.text-ctn {
			padding: 20px;

			p {
				color: var(--color-text);
				font-family: var(--font-secondary);
				font-size: 13px;
				line-height: 18px;
			}

			.location {
				padding-top: 10px;
				font-family: var(--font-secondary);
				font-size: 20px;
				font-weight: 600;
				line-height: 27px;
			}
		}

		.main-button {
			padding: 17px 48px;
		}

		#close-popup {
			position: absolute;
			right: 0;
			top: 0;
			font-size: 26px;
			height: 30px;
			width: 30px;
			text-align: center;
			background-color: var(--primary-color);
			color: white;
		    display: flex;
		    align-items: center;
		    justify-content: center;
		    cursor: pointer;
		    transition: all 0.4s ease;

		    &:hover {
				background-color: rgba(var(--primary-color), 0.5);
		    }
		}
	}

	@media (max-width: 992px) {

		&.style-1 {
			#map {
				position: absolute;
				z-index: -1;
				opacity: 0;
				display: block;
			}
		}
		

		&.show {
			&.style-1 {
				#map {
					opacity: 1;
					z-index: initial;
					position: relative;
				}
			}
		}
	}
}

.neighborhoods-filters-toggle-ctn {
    display: none;
    align-items: center;
    justify-content: space-between;
    padding: 0px;
	background-color: white;
	padding: 14px 20px;
	border-bottom: 1px solid #dddddd;
	z-index: 2;

    .view-toggle-ctn {
      padding: 2px;
      border-radius: 6px;
      background-color: #eaeaea;

		a {
			display: inline-block;
			color: var(--color-text);
			font-family: var(--font-secondary);	
			font-size: 16px;
			font-weight: 600;
			letter-spacing: 0;
			padding: 7px 20px;
			cursor: pointer;

			&.active {
				color: var(--primary-color);
				border-radius: 5px;
				background-color: #ffffff;
				box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.2);
			}
		}
	}

	@media(max-width: 992px) {
		display: flex;
	}
}