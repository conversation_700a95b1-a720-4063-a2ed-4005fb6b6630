/*
	Properties Featured
*/

$propertiesFeaturedBG: $white;

$propertyHeroButtonHeight: 50px;
$propertyHeroButtonWidth: 50px;
$propertyHeroButtonHeightMobile: 40px;
$propertyHeroButtonWidthMobile: 40px;
$propertyHeroRadius: 40px;
$propertyHeroLeftButtonPosition: 20px;
$propertyHeroLeftButtonPositionMobile: 15px;
$propertyHeroRightButtonPosition: 20px;
$propertyHeroRightButtonPositionMobile: 15px;
$propertyHeroSettingsBackground: rgba(0,0,0,0.5);
$propertyHeroSettingsColor: white;


.properties-featured-cpn{
    padding: 130px 0;
    background-color: $propertiesFeaturedBG; 

    .slider {
        @media (min-width:992px) {
            padding-left: grid-space(math.div(1,12), 0);
        }
    }

    .properties-featured-swiper {
        overflow: hidden;
        position: relative;
    }

    .icon-tab {
		position: absolute;
		top: 10px;
		right: 10px;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.play-btn {
		position: relative;
		margin-left: 10px;
		width: 35px;
		height: 35px;
		border-radius: 100%;
		background-color: rgba(0,0,0,0.5);
		cursor: pointer;
		color: white;
		font-size: 20px;
		display: flex;
		justify-content: center;
		align-items: center;

		.icon-play {
			width: 16px;
		}
	}

	.player360 {
		position: relative;
		margin-left: 10px;
		width: 35px;
		height: 35px;
		border-radius: 100%;
		background-color: rgba(0,0,0,0.5);
		cursor: pointer;
		color: white;
		font-size: 20px;
		display: flex;
		justify-content: center;
		align-items: center;

		a {
			color: white;
		}
	}

    .title{
        margin-top: 32px;
        margin-bottom: 56px;
    }

    .description{
        margin: 0;
        color: var(--color-text);
    }

    @media (max-width:992px) {

        .title{
            text-align: center;
        
            .icon{
                margin: 0 auto 30px; 
            }
        }

        .description{
            text-align: center;
        }

        .small-link{
            display: inline-block;
            margin: 0 auto 40px;
            text-align: center;
        }
        
    }

    .swiper-btn{
        width: $propertyHeroButtonWidth;
        height: $propertyHeroButtonHeight;
        background: $propertyHeroSettingsBackground;
        border-radius: $propertyHeroRadius;
        transform: translate(0%, -50%);
        margin-top: 0;
        transition: all 0.4s ease;

        @media (max-width:992px) {
            width: $propertyHeroButtonWidthMobile;
            height: $propertyHeroButtonHeightMobile;
        }

        &:before {
            position: absolute;
            font-family: 'icomoon' !important;
            speak: none;
            font-style: normal;
            font-weight: normal;
            font-variant: normal;
            text-transform: none;
            line-height: 1;
            font-size: 28px;
            color: $propertyHeroSettingsColor;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);

            @media (max-width:992px) {
                font-size: 20px;
            }
        }

        &.hidden{
            display: none;
        }

        &.swiper-button-disabled{
            opacity: 0 !important;
        }

        &.swiper-button-prev {
            left: $propertyHeroLeftButtonPosition;

            @media (max-width:992px) {
                left: $propertyHeroLeftButtonPositionMobile;
            }

            &:before {
                content: "\e909";
            }
        }

        &.swiper-button-next {
            right: $propertyHeroRightButtonPosition;

            @media (max-width:992px) {
                right: $propertyHeroRightButtonPositionMobile;
            }

            &:before {
                content: "\e90a";
            }
        }
    }


    .property-featured-cpn{
        position: relative;
        display: block;

        .img-ctn{
            img{
                width: 100%;
                border-radius: 32px;
            }
        }

        .properties-info{
            position: absolute;
            background-color: white;
            right: 0;
            bottom: 0;
            max-width: 70%;
            width: 100%;

            .bloc-head {
                padding: 20px 24px 0;
                display: flex;
                justify-content: space-between;
                align-items: center;
    
                .price {
                    font-size: 22px;
                    font-weight: 700;
                    line-height: normal;
                    white-space: nowrap;
                    margin: 0;
                }
    
                .numbers {
                    font-family: var(--font-primary);
                    @extend .-cta-medium;
                    text-align: right;
                    display: flex;
                    width: 100%;
                    justify-content: flex-end;
    
                    .icon-ctn {
                        padding-left: 32px;
                        display: flex;
                        align-items: center;
                        color: var(--color-text);

                        p {
                            margin:0;
                            padding-left: 12px;
                            font-family: var(--font-primary);
                            @extend .-cta-medium;
                        }
                    }
                }
            }

            .location{
                padding: 0 24px;
                margin: 0;
                color: var(--color-text);
                @extend .--normal;
            }
    
            .more-info {
                position: relative;
                margin: 0 24px;
                margin-top: 16px;
                padding-top: 16px;
                border-top: solid 1px #CBC7B5;
                display: flex;
                justify-content: space-between;

                .button{
                    width: 50px;
                    height: 100%;
                    background-color: var(--primary-color);
                    transition: all 0.5s ease-in-out;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    position: absolute;
                    right: 0;
                    z-index: 5;
                    display: none;

                    i{
                        color: white;
                    }

                    &:hover{
                        cursor: pointer;
                        background-color: var(--primary-color-darker);
                    }
                }
    
                .inner {
                    display: flex;
                    margin-right: 10px;

                    .address, .type{
                        @extend .--small;
                        margin: 0;
                        margin-bottom: 20px;
                    }

                    .type{
                        margin-left: 25px;
                    }
                }
            }

            @media (max-width:600px) {
                position: relative;
                max-width: 100%;
            }
           
        }
    }
}