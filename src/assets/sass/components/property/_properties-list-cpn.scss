/*
	Properties List
*/

$propertiesListBG: $white;

.properties-list-cpn {
	background-color: $propertiesListBG;
	padding-top: 120px;
	padding-bottom: 56px;

	@media (max-width: 992px) {
		padding-top: 60px;
		padding-bottom: 60px;
	}
	
	.cpn-head {
		margin-bottom: 56px;
		display: flex;
		justify-content: space-between;
		align-items: center;

		.title-ctn {
			display: flex;
			align-items: center;
			gap: 24px;

			@media (max-width: 767px) {
				gap: 16px;
			}
		}

		.title {
			margin-top: 0;
			margin-bottom: 0;
			@extend .-h3;
		}
	}

	.main-button {
		margin: 0 auto;
		margin-top: 28px;
		text-transform: none;
	}
}

.properties-list-ctn {
	display: flex;
	flex-flow: row wrap;

	.small-link {
		padding-right: 0px;
	}
}

.properties {
	position: relative;
	margin-right: 40px;
	margin-bottom: 40px;
	width: calc(100% * 1/3 - 40px);
	overflow: hidden;
	cursor: pointer;
	border-radius: 16px;

	//If Desktop apply this
	@media (min-width: 992px) {
		&:nth-child(3n) {
			margin-right: 0;
		}
	}

	@media (max-width: 992px) {
		width: calc(100% * 1/2 - 10px);

		&:nth-child(2n) {
			margin-right: 0;
		}
	}

	@media (max-width: 767px) {
		width: 100%;
		margin-right: 0 !important;
	}

	.img-ctn {
		border-radius: 16px;

		img{
			width: 100%;
			height: auto;
			border-radius: 16px;
		}
	}

	&:hover {
		.filter {
			background-color: rgba(0,0,0,0.3);
			opacity: 1;
		}

		.properties-info {
			max-height: 115px;

			.more-info {
				max-height: 250px;
				opacity: 1;
			}
		}
	}

	.gradiant {
		position: absolute;
		height: 50%;
		width: 100%;
		left: 0;
		right: 0;
		bottom:0;
		top: 50%;
		background: linear-gradient(180deg, rgba(0,0,0,0) 0%, #000000 100%);
	}

	.filter {
		position: absolute;
		height: 100%;
		width: 100%;
		left: 0;
		right: 0;
		top: 0;
		bottom:0;
		transition: $propertyAnimation;
		transition-duration: 0.3s;
		background-color: rgba(0,0,0,0);

		@media (max-width: 992px) {
			opacity: 0;
			background-color: rgba(0,0,0,0.3);
		}
	}

	.icon-tab {
		position: absolute;
		top: 10px;
		right: 10px;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.play-btn {
		position: relative;
		margin-left: 10px;
		width: 35px;
		height: 35px;
		border-radius: 100%;
		background-color: rgba(0,0,0,0.5);
		cursor: pointer;
		color: white;
		font-size: 20px;
		display: flex;
		justify-content: center;
		align-items: center;

		.icon-play {
			width: 16px;
		}
	}

	.player360 {
		position: relative;
		margin-left: 10px;
		width: 35px;
		height: 35px;
		border-radius: 100%;
		background-color: rgba(0,0,0,0.5);
		cursor: pointer;
		color: white;
		font-size: 20px;
		display: flex;
		justify-content: center;
		align-items: center;

		a {
			color: white;
		}
	}

	.properties-info {
		position: absolute;
		z-index: 4;
		bottom: 0;
		left: 0;
		right: 0;
		transition: $propertyAnimation;
		max-height: 200px;

		@media (max-width: 992px) {
			max-height: 250px !important;
		}

		p {
			margin: 0;
			color: white;
			font-family: var(--font-secondary);
		}

		.bloc-head {
			padding: 20px;
			display: flex;
			justify-content: flex-start;
			align-items: center;

			.price {
				font-family: var(--font-primary);
				font-size: var(--font-size-price-small);
				font-weight: 600;
				line-height: var(--line-height-price-small);
				white-space: nowrap;
				border-right: 1px solid $white;
				padding-right: 20px;
				margin-right: 20px;

				.small-tps{
					font-size: 10px;
					position: relative;
    				top: -5px;
				}
			}

			.type {
				font-family: var(--font-primary);
				@extend .-cta-small;
			}
		}

		.more-info {
			position: relative;
			top: -15px;
			padding: 0 20px;
			overflow: hidden;
			max-height: 0;
			text-align: left;
			transition: $propertyAnimation;
			opacity: 0;

			@media (max-width: 992px) {
				max-height: 250px;
				opacity: 1;
			}

			a.address {
				display: flex;
				flex-direction: column;
				padding: 10px 0;

				.address-text, .location {
					font-size: var(--font--size-description);
					line-height: var(--line-height-description);
					color: white;
				}
			}

			.align {
				display: flex;
				justify-content: space-between;
				align-items: center;

				.numbers {
					display: flex;

					.icon-ctn {
						padding-left: 20px;
						display: flex;
						align-items: center;

						&:first-child {
							padding-left: 0;
						}

						i {
							// font-size: $primaryTextSize;
							color: white;
							margin-right: 6px;
						}

						p {
							font-family: var(--font-primary);
							@extend .-cta-small;
						}
					}
				}
			}
		}
	}
}

.containerBandeau {
	display: flex;
	flex-wrap: wrap;
	gap: 2px;
	position: absolute;
	z-index: 5;
	top: 16px;
	left: 16px;

	.properties-label {
		margin-right: 5px;
		padding: 8px 16px;
		border-radius: 3px;
		font-family: var(--font-primary);
		@include fontSize(14px, 19px, 700);
		color: $white;
		text-transform: uppercase;
	
		&.-label-sold { background: $red; }
		&.-label-rental-possible { background: var(--primary-color); }
		&.-label-new { background: var(--primary-color); }

		&.-label-view {
			// padding: 4px 12px;
			background: var(--primary-color);
			// @include fontSize(13px, 18px, 600);
			text-transform: none;
		}
	}	
}


.home-properties-list {
	.properties-list-cpn {
		background-color: $background;

		.cpn-head {
			justify-content: center;
		}
	}
}