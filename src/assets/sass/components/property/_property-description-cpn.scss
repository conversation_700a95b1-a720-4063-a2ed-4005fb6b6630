/*
	Property Description Component
*/
$propertyDescriptionInfoSize: var(--font-size-medium);
$propertyDescriptionInfoLineHeight: var(--line-height-medium);
$propertyDescriptionInfoColor: var(--color-text);

$propertyDescriptionInfoIconBorderColor: #dcdcdc;
$propertyDescriptionInfoIconSize: 18px;
$propertyDescriptionInfoIconLineHeight: 24px;

.property-description-cpn {

  .location {
    display: flex;
    flex-wrap: wrap;
    gap: 14px;
    margin: 25px 0 0;
    @extend .--normal;
    font-family: var(--font-primary);
    color: $propertyDescriptionInfoColor;
    font-weight: 400;

    .municipality, .type{
      border-right: 1px solid $propertyDescriptionInfoColor;
      padding-right: 12px;

      @media (max-width: 992px) {
        border-right: none;
        padding-right: 0;
        margin-bottom: 10px;
      }
    }

    .mls {
      @extend .--normal;
    }
  }

  .icon-info-ctn {
    margin-top: 30px;
    padding: 20px 70px 20px 0;
    border-top: 1px solid $propertyDescriptionInfoIconBorderColor;
    border-bottom: 1px solid $propertyDescriptionInfoIconBorderColor;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .icon-wrap {
      display: flex;
      align-items: center;

      i {
        color: var(--primary-color);
        font-size: 22px;
      }

      p {
        margin: 0;
        margin-left: 15px;
        font-family: var(--font-primary);
        @extend .--large;
      }
    }
  }

  .description {
    margin-top: 40px;
    margin-bottom: 0;
    color: var(--color-text);
    font-size: $propertyDescriptionInfoSize;
  }

  @media (max-width: 767px) {

    .icon-info-ctn {
      padding-right: 0px;
      .icon-wrap { text-align: center; }
    }
  }
}
