$backgroundColor: #F6F6F6;

.property-contact-form{
	display: flex;
	align-items: center;
	justify-content: center;

  .close-contact{
		position: absolute;
		right: 0;
		top: 0;
		background-color: $primarySmallButtonBG;
		color: white;
		width: 40px;
		height: 40px;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 18px;

		&:hover{
			cursor: pointer;
			background-color: $primarySmallButtonBGHover;
		}

    	span{
      		font-size: 18px;
    	}
	}

	&-ctn{
		background-color: $backgroundColor;
		width: 100%;
		height: 100%;
		padding: 60px 100px;
		max-width: 780px;
		transition: $primaryAnimation;
		opacity: 1;
		position: relative;

		&.send{
			opacity: 0;
		}

		.loading-inner{
			opacity: 0.5;
		}

		@media (max-width:990px) {
			padding: 50px;
		}

		@media (max-width:425px) {
			padding: 30px 10px;
		}

		.contact-form{
			@media (max-width:768px) {
				margin-top: 30px;
			}

			@media (max-width:425px) {
				margin-top: 20px;

				.input-ctn textarea{
					height: 70px;
				}

				.main-button{
					min-width: initial;
					width: 100%;
					display: inline-block;
					padding: 13px;
				}
			}

			.main-button{
				min-width: initial;
				width: 100%;
				display: inline-block;
				padding: 15px;
			}

			.form-row{
				@media (max-width:425px) {
					margin-bottom: 10px;
				}
			}

			.ng-input {
				padding-left: 15px;
				input {
					padding: 0;
				}
			}

			.mydp .selection {
				padding-left: 15px;
				font-family: var(--font-secondary);
				font-size: $inputSize !important;
				color: $inputColor;
			}
		}
	}

	&-broker-card{
		.img-ctn{
			border-radius: 100%;
			overflow: hidden;
			max-width: 150px;
			max-height: 150px;
			margin: 0 auto 30px;

			&.hide{
				display: none;
			}

			@media (max-width:425px) {
				max-width: 80px;
				max-height: 80px;
				margin: 0 auto 10px;
        display: none;
			}

			@media (max-width:320px) {
				display: none;
			}
		}

		.broker-info-ctn{
			text-align: center;

			p{
				margin: 0;
			}

			.name {
				padding-bottom: 5px;
				color: $black;
				font-family: var(--font-secondary);
				font-weight: 600;
				line-height: 28px;
			}

			.role {
				padding-bottom: 10px;
				color: $light-grey;
				font-family: var(--font-secondary);
				font-size: 13px;
				line-height: var(--line-height-cta-small);
			}

			.number {
				color: var(--color-text);
				font-family: var(--font-secondary);
				font-size: var(--font-size-small);
				font-weight: 600;
				line-height: 22px;

				i {
					margin-right: 12px;
				}
			}
		}
	}

	.form-loader{
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%,-50%);
	}

	.form-response{
		opacity: 0;
		position: absolute;
		background-color: #F6F6F6;
		width: 100%;
		padding: 60px 100px;
		max-width: 780px;
		text-align: center;
		transition: $primaryAnimation;
		z-index: -1;
    	left: 50%;
    	transform: translateX(-50%);

		.warning-message{
			font-size: 14px;
			color: var(--color-text);
			margin-bottom: 20px;
		}

		.button-ctn{
			.main-button{
				margin: 0 auto;
			}
		}

		&.show{
			opacity: 1;
			z-index: 12;
		}
	}
}




