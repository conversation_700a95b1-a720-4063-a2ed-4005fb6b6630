/*
	Property Navigation Component
*/

.property-navigation-cpn {
	background-color: $background;
	display: flex;
	align-items: center;
	overflow: hidden;
	padding: 32px 20px;

	.previous-ctn, .next-ctn {
		margin: 0;
		width: 50%;
		display: flex;
		align-items: center;
	}

	.next-ctn {
		.property-nav-ctn {
			border-left: 1px solid $light-grey;
			justify-content: flex-end;

			.text-ctn {
				padding-left: 0;
				padding-right: 40px;
				text-align: right;

				@media (max-width: 767px) {
					padding-right: 20px;
				}
			}
		}

		.-right-nav {
			float: right;
		}
	}

	.property-nav-ctn {
		position: relative;
		width: 100%;

		&:hover {
			cursor: pointer;
			.arrow-ctn {
				opacity: 1;

				&.-left-arrow {
					left: 0px;
				}

				&.-right-arrow {
					right: 0px;
				}
			}

			.nav-info {
				&.-left-nav {
					left: 40px;

					@media (max-width: 767px) {
						left: 0px;
					}
				}
				&.-right-nav {
					right: 40px;

					@media (max-width: 767px) {
						right: 0px;
					}
				}
			}
		}

		.arrow-ctn {
			z-index: 4;
			position: absolute;
			width: 40px;
			height: 100%;
			color: $black;
			display: flex;
			align-items: center;
			justify-content: center;
			opacity: 0;
			transition: all 275ms cubic-bezier(0.420, 0.000, 1.000, 1.000);

			@media (max-width: 767px) {
				display: none;
			}

			i {
				font-size: 15px;
			}

			&.-left-arrow {
				left: -40px;
			}

			&.-right-arrow {
				right: -40px;
			}
		}

		.nav-info {
			position: relative;
			display: flex;
			align-items: center;
			transition: all 275ms cubic-bezier(0.420, 0.000, 1.000, 1.000);

			.title {
				font-size: 20px;
				font-weight: 600;
				line-height: normal;
			}

			&.-left-nav {
				left: 0px;
			}
			&.-right-nav {
				right: 0px;
			}
		}

		.img-ctn {
			position: relative;
			height: 160px;
			width: 160px;
			overflow: hidden;
			border-radius: 16px;

			@media (max-width: 767px) {
				display: none;
			}

			img {
				position: absolute;
				height: 100%;
				max-width: none;
				left: 50%;
				transform: translate(-50%, 0%);
        		object-fit: cover;
				border-radius: 16px;
			}
		}

		.text-ctn {
			padding-left: 40px;

			@media (max-width: 767px) {
				padding-top: 30px;
				padding-bottom: 30px;
				padding-left: 20px;
			}

			p {
				margin: 0;
			}

			.location {
				color: var(--color-text);
				@extend .--small;
			}
		}
	}
}
