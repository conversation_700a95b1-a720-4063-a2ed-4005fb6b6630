/*
	Location Pane Component
*/

.location-pane-cpn {
	position: absolute;
	margin: 0;
	z-index: 10;
	width: 620px;
	height: 100%;
	background-color: $background;
	box-shadow: 2px 0 4px 0 rgba(0,0,0,0.10);
	transition: $primaryAnimation;
	margin-left: -620px;
	display: flex;
	align-items:center;

	@media (max-width: 767px) {
		display: none;
	}

	.toggle-pane {
		position: absolute;
		top: 15px;
		right: -30px;
		display: block;
		width: 30px;
		line-height: 60px;
		text-align: center;
		background-color: var(--primary-color);
		color: white;
		transition: $primaryAnimation;
		cursor:pointer;

		div {
			transform: rotate(180deg);
		}

		&:hover {
			background-color: rgba(var(--primary-color), .60);
		}
	}
	&.open {
		margin: 0;

		.toggle-pane {
			div {
				transform: rotate(0);
			}
		}
	}

	.pane-content-ctn {
		padding: 0px 40px;

		.info-ctn {
			margin-top: 30px;
			padding-bottom: 30px;
			border-bottom: 1px solid #DCDCDC;

			&:last-child {
				padding-bottom: 0;
				border-bottom: none;
			}

			.sub-section-title {
				margin-top: 0;
				margin-bottom: 10px;
				color: $black;
				@extend .-thumbnail-meta;
			}

			.location {
				margin: 24px 0;
				color: $black;
				@extend .-h6;
			}

			.direction {
				
				.icon-arrow-right {
					margin-left: 12px;
					transition: $primaryAnimation;
				}

				&:hover {
					
					.icon-arrow-right {
						transform: translateX(10px);
					}
				}
			}

			.description {
				margin-top: 0;
				margin-bottom: 15px;
				color: var(--color-text);
				@extend .--small;
			}
		}
	}
}
