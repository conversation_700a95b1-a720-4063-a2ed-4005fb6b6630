/*
	Property Group Card Component
*/

$propertyGroupsCardBG: #FFFFFF;
$propertyGroupsCardShadow: 0 2px 6px 0 rgba(0,0,0,0.1);

.propertygroup-card-cpn {
	padding: 64px;
	margin-bottom: 40px;
	border-radius: 32px;
	background-color: $propertyGroupsCardBG;
	@include clearfix;
	cursor: pointer;

	@media (max-width: 992px) {
		padding: 32px 20px;
	}

	&:last-child {
		margin-bottom: 0;
	}

	.img-ctn {
		border-radius: 16px;

		a { 
			width: 100%;
			height: 100%; 			
		}

		img { 
			border-radius: 16px;
			@include img(); 
			object-fit: cover;
			aspect-ratio: 4/3;
		}
	}

	.card-content {
		padding-right: 0;

		@media (max-width: 767px) {
			padding: 0;
		}

		h3 {
			font-family: var(--font-primary);
			font-size: 36px;
			font-style: normal;
			font-weight: 400;
			line-height: 46px;
			margin: 0;
		}
	}

	.description {
		margin: 40px 0;
		@extend .--normal;
	}
}