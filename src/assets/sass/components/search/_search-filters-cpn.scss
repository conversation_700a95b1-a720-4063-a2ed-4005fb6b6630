.search-filters-cpn {
  display: flex;
  flex-direction: column;

  .main-button {
    width: auto;
    min-width: auto;
    height: $inputHeight;
    padding: 12px 25px;
    font-family: var(--font-primary);
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    text-transform: none;
    cursor: pointer;
    &.apply {
      margin-left: 15px;
    }
    &.clear {
      span {
        margin-left: 10px;
      }
    }
    @media (max-width: 425px) {
      padding: 12px 15px;
    }
  }

  .filters-toggle-ctn {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0px;

    .main-button.filters-toggle img {
      margin-right: 6px;
    }

    .view-toggle-ctn {
      display: none;
      padding: 2px;
      border-radius: 6px;
      background-color: #eaeaea;
      a {
        display: inline-block;
        color: var(--color-text);
        font-family: var(--font-secondary);
        font-size: 16px;
        font-weight: 600;
        letter-spacing: 0;
        padding: 7px 20px;
        cursor: pointer;
        &.active {
          color: var(--primary-color);
          border-radius: 5px;
          background-color: #ffffff;
          box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.2);
        }
      }
    }

    .main-button.filters-toggle {
      min-width: 110px;
      display: flex;
      align-items: center;
      justify-content: center;
      position: absolute;
      right: 22px;
      top: 20px;
      font-family: var(--font-primary);
      font-size: 15px;
      font-weight: 600;
      line-height: normal;
      span {
        margin-right: 6px;
      }
    }

    @media (min-width: 768px) and (max-width: 992px) {
      .view-toggle-ctn {
        position: absolute;
        top: 20px;
        right: 150px;
      }
    }
    @media (max-width: 992px) {
      .view-toggle-ctn {
        display: block;
      }
    }
    @media (max-width: 767px) {
      padding: 14px 20px;
      border-bottom: 1px solid #dddddd;

      .main-button.filters-toggle {
        position: relative;
        top: auto;
        right: auto;
      }
    }
  }

  // SEARCH INPUT
  .form-group-search {
    position: relative;
    width: 100%;
    margin-right: 10px;

    > span {
      position: absolute;

      &.icon-search,
      &.icon-close {
        left: 10px;
        top: 50%;
        margin-top: -12px;
        color: $light-grey;
        z-index: 99;
        font-size: 24px;
      }

      &.icon-close {
        top: 26px;
        color: var(--color-text);
        font-size: 14px;
        cursor: pointer;
      }
    }

    input {
      width: 100%;
      padding: 0 15px 0 50px;
      border: 0px none;
      color: $inputColor;
      font-family: var(--font-primary);
      font-size: 17px;
      font-weight: 400;
      line-height: 28px;
    }

    input::placeholder {
      color: $inputColor;
      font-family: var(--font-primary);
      font-size: 17px;
      font-weight: 400;
      line-height: 28px;
    }

    ul.search-list-dropdown {
      display: block;
      position: absolute;
      width: 100%;
      margin: 0;
      top: calc(100% + 20px);
      padding: 0px;
      z-index: 999;
      @media (max-width: 767px) {
        margin-top: -10px;
      }
      li a {
        display: block;
        padding: 20px 40px;
        background-color: white;
        border: 1px solid #dddddd;
        border-bottom: 0px none;
        color: #000;
        z-index: 1000;
      }
      li a:hover {
        background-color: var(--primary-color);
        color: white;
      }
      li:last-child {
        border-bottom: 1px solid #dddddd;
      }
    }
  }

  //SLIDER
  .slider-picker {
    margin: 0 0;
    vertical-align: middle;
    height: 5px;
    border: 0;
    border-radius: 0;
    box-shadow: none;
    background-color: #d8d8d8;

    .noUi-connect {
      background: var(--primary-color);
      box-shadow: none;
    }

    .noUi-origin {
      top: -5px;
    }

    .noUi-handle {
      &::before,
      &::after {
        display: none;
      }
      &:focus {
        outline: none;
      }
    }

    .noUi-tooltip {
      cursor: pointer;
      width: 70px;
      padding: 0;
      line-height: 40px;
      border-radius: 30px;
      font-weight: 600;
      font-size: 13px;
      color: $black;
      bottom: 50%;
      margin-bottom: -21px;
    }
  }

  .section-search-bar {
    display: flex;
    width: 100%;
    padding: 20px;
    border-top: 1px solid $lighter-gray;
    border-bottom: 1px solid $lighter-gray;
    @media (max-width: 767px) {
      padding: 10px;
    }

    .property-category-filter,
    .types-filter {
      flex-shrink: 0;
      width: 140px;
      margin-right: 10px;
      @media (max-width: 768px) {
        margin: 0px 10px 20px 0px;
      }
    }
  }

  // Search bar dropdowns
  .base-filters {
    display: flex;
    position: absolute;
    right: 150px;
    top: 20px;
    gap: 20px;

    &.hidden {
      display: none;
    }

    .ng-value {
      font-size: 16px;
    }

    .ng-select {
      width: 180px;

      .ng-select-container .ng-value-container .ng-input>input, .ng-value-label, .ng-option-label {
        font-family: var(--font-primary);
        font-size: 16px;
        font-weight: 400;
        line-height: 24px;
      }
    }
    
    @media (max-width: 992px) {
      position: relative;
      right: auto;
      top: auto;
      display: flex;
      justify-content: space-between;
      margin: 20px;
      gap: none;

      .ng-select {
        width: 48%;
      }
    }
  }

  .filter-fields {
    display: flex;
    flex-direction: row;
    width: 100%;
    margin: 30px 0px 20px;
    background-color: white;

    .filter-column {
      flex: 1;
      padding: 0px 20px;
      border-right: 1px solid #dddddd;
      h5 {
        margin: 0px 0px 12px 0px;
        text-transform: uppercase;
        color: #393939;
        &.slider {
          padding-bottom: 24px;
        }
        &:not(:first-child) {
          margin-top: 20px;
        }
      }
      p {
        padding: 20px;
        background-color: #eee;
        margin: 50px 0px 30px 0px;
        font-size: 80%;
      }

      .na-label {
        margin: 0;
        padding: 0;
        font-family: var(--font-secondary);
        font-size: 15px;
        color: var(--color-text);
        line-height: 40px;
        background-color: transparent;
      }
      .custom-checkbox-group {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        margin-bottom: 16px;
        > div {
          height: auto;
          width: 49%;
          min-width: 45%;
          display: flex;
          margin: 5px 0px;
        }
        label {
          line-height: 10px;
          line-height: 18px;
          vertical-align: top;
          &::before {
            position: absolute;
            margin-top: 1px;
          }
          &:hover::before {
            border-radius: 3px;
          }
          span {
            margin-left: 25px;
            display: inline-block;
            font-size: 16px;
            font-weight: 400;
            line-height: 24px; 
          }
        }
        .custom-checkbox:checked + label::after {
          top: 8px;
        }
        &:empty {
          display: none;
        }
      }
      .caracteristiques-group {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: space-between;
        .caracteristiques-filter {
          width: 48%;
          margin: 10px 0px;
        }
      }
      .real-estate-alert {
        font-size: 0.8em;
        padding: 30px;
        margin-top: 40px;
        background-color: #f4f4f4;
        display: flex;
        flex-direction: col;
        align-items: flex-start;
        a {
          color: var(--primary-color);
          font-weight: bold;
        }
        img {
          width: 70px;
          margin: 8px 20px 0px 0px;
        }
      }
      .slider-wrapper {
        margin: 0px 50px 40px 20px;
      }
      &:last-child {
        border-right: 0px none !important;
      }
    }

    @media (min-width: 769px) and (max-width: 992px) {
      .caracteristiques-group > .caracteristiques-filter,
      .custom-checkbox-group > div {
        width: 100% !important;
      }
    }

    @media (max-width: 768px) {
      flex-direction: column;
      .filter-column {
        h5 {
          margin: 30px 0px 10px 0px !important;
        }
        &:first-child h5 {
          margin-top: 0px !important;
        }
      }
    }
  }

  .filter-footer {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 30px;
    margin-right: 22px;
  }
}
