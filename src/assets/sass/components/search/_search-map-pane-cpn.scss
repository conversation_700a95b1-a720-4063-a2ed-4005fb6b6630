.search-map-pane-cpn {
	#map {
		position: fixed;
    width: 100%;
    min-height: 500px;
		height: 100%;
		right: 0;
    top: 80px;
		transition: $primaryAnimation;

		.mapbox-logo { display: none; }
    .mapboxgl-ctrl-top-right { top: 160px; }

    .inner-popup {
      display: flex;
      cursor: pointer;

      img {
        width: 70px;
        border-radius: 16px;
      }

      .content {
        padding-left: 12px;

        .price {
          margin-top: 0;
          margin-bottom: 5px;
          color: $black;
          font-family: var(--font-primary);
          font-size: 16px;
          font-weight: 700;
          line-height: normal;
        }
  
        .location {
          margin: 0;
          color: var(--color-text);
          font-family: var(--font-primary);
          font-size: 13px;
          font-weight: 400;
          line-height: normal;
        }
      }
    }

    .mapboxgl-popup-close-button {
      right: 10px;
      top: 3px;
      font-size: 22px;
      color: $black;
    }

    .mapboxgl-popup-content {
      background-color: $white;
      border-radius: 16px;
      padding: 10px;
    }
	}

	@media (max-width: 992px) {
    #map { 
      display: none;
      position: relative; 
      top: 0;

      .mapboxgl-ctrl-top-right { top: 20px; }
    }

		&.show #map { display: block; }
	}
}
