.search-sell-small-cpn {
	display: flex;
	align-items: center;
	min-height: 560px;
	padding: 120px 0;

  @media (max-width: 992px) { 
    padding: 40px 0;
  }

  .image {
    display: flex;
    justify-content: center;
    align-items: center;
    padding-left: grid-space(math.div(1,12), 0);

    @media (max-width: 992px) {
      padding-left: 0;
    }
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 32px;

      @media (max-width: 992px) {
        max-height: 450px;
      }
    }
  }

  .subtitle {
    color: var(--secondary-color);
    @extend .-eyebrow;
  }

  .title {
    margin: 0px;
    margin-top: 32px;
    margin-bottom: 56px;
  }

  p { 
    margin: 0;
  }

  .input-ctn { 
    margin: 40px 0px;
    position: relative; 

    input {
      border: none;
      border-radius: 16px;
      background: $background;
      padding: 24px 32px;
      height: 78px;

      &, &::placeholder {
        opacity: 1;
        font-family: var(--font-primary);
        color: var(--primary-color);
        @extend .--normal;
      }
    }

    &::after {
      font-family: 'icomoon' !important;
      content: '\e91a';
      display: block;
      position: absolute;
      top: 25px;
      right: 32px;
      font-size: 24px;
      color: $black;
    }
  }

  .main-button {
    margin-top: 32px;
  }
}