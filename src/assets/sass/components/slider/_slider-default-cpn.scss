$sliderHeight: auto;

.slider-default-cpn{
    display: inline-table;
    width: 100%;
    
    .swiper-container{
        width: 100%;
        height: $sliderHeight;

        .swiper-btn{
            width: $propertyHeroButtonWidth;
            height: $propertyHeroButtonHeight;
            background: $propertyHeroSettingsBackground;
            border-radius: $propertyHeroRadius;
            transform: translate(0%, -50%);
            margin-top: 0;
            transition: all 0.4s ease;
    
            @media (max-width:992px) {
                width: $propertyHeroButtonWidthMobile;
                height: $propertyHeroButtonHeightMobile;
            }
    
            &:before {
                position: absolute;
                font-family: 'icomoon' !important;
                speak: none;
                font-style: normal;
                font-weight: normal;
                font-variant: normal;
                text-transform: none;
                line-height: 1;
                font-size: 22px;
                color: $propertyHeroSettingsColor;
                left: 50%;
                top: 50%;
                transform: translate(-50%, -50%);
    
                @media (max-width:992px) {
                    font-size: 20px;
                }
            }
    
            &.swiper-button-prev {
                left: $propertyHeroLeftButtonPosition;
    
                @media (max-width:992px) {
                    left: $propertyHeroLeftButtonPositionMobile;
                }
    
                &:before {
                    content: "\e909";
                }
            }
    
            &.swiper-button-next {
                right: $propertyHeroRightButtonPosition;
    
                @media (max-width:992px) {
                    right: $propertyHeroRightButtonPositionMobile;
                }
    
                &:before {
                    content: "\e90a";
                }
            }
        }

        .swiper-slide{
            width: 100% !important;

            .image{
                width: 100%;
                height: 100%;
                object-fit: cover;
                border-radius: 32px;
            }

            .slide-info{
                position: absolute;
                left: 0;
                bottom: 0;
                max-width: 70%;
                background-color: white;
                padding: 8px 30px;
                
                *{
                    color: var(--color-text);
                    font-size: 14px;
                    line-height: 24px;
                    margin: 0;
                }

                @media(max-width: 768px){
                    max-width: 90%;
                }
            }
        }
    }
}