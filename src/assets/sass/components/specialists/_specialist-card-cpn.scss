/*
	Specialist Card Component
*/

.specialist-card-cpn {
	position: relative;
	width: 100%;

	.specialist-content {
		padding: 64px;
		width: 100%;
		display: inline-block;
		box-shadow: 0 2px 4px 0 rgba(0,0,0,0.1);
		background-color: $white;
		border-radius: 32px;
		margin-bottom: 40px;

		@media screen and (max-width: 768px) {
			padding: 30px;
			margin-bottom: 20px;
		}
	}

	.specialist-wrap {
		display: block;
		position: relative;
		width: 100%;
		margin-bottom: 20px;
		border-radius: 3px;
	}

	.img-ctn {
		text-align: left;
		img {
			margin-bottom: 40px;
			max-height: 280px;
		}
	}

	.title {
		margin: 0;
	}

	.description {
		margin: 32px 0;
	}

	.user-ctn {
		.name {
			margin: 0;
			color: $black;
			font-family: var(--font-secondary);
			font-size: 20px;
			font-weight: 500;
			line-height: 36px;
		}

		.address {
			margin: 0;
			@extend .-small;
		}

		.contact-ctn {
			display: flex;
			align-items: center;
			margin-top: 32px;

			@media (max-width:570px) {
				flex-wrap: wrap;
			}

			a {
				margin-right: 35px;
				@extend .-cta-medium;
				font-weight: 600;

				@media (max-width:570px) {
					width: 100%;
					margin-bottom: 15px;
				}

				&:last-child {
					margin-right: 0;
				}

				&.mobile {
					display: flex;
					align-items: center;

					i {
						font-size: 18px;
						margin-right: 5px;
					}
				}

				&.website {
					display: flex;
					align-items: center;

					i {
						font-size: 18px;
						margin-right: 5px;
					}
				}

				&.icon-mail {
					font-size: 16px;
				}
			}
		}
	}
}
