$awardsSliderBG: var(--color-bg);

.awards-slider-cpn {

	h2 {
		margin: 0;
	}

	.awards-slider-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		border-bottom: 1px solid var(--primary-color);
		padding-bottom: 16px;
		margin-bottom: 135px;

		@media (max-width: 767px) {
			margin-bottom: 64px;
			flex-direction: column;
			align-items: flex-start;
			gap: 16px;
		}
	}

	.swiper-container {
		overflow: hidden;
	}

	.swiper-slide {
		margin-bottom: 4px;
		height: auto;
	}

	.nav-ctn {
	}

	.swiper-btn {
		position: relative;
		display: inline-block;
		height: 50px;
		width: 50px;
		top: inherit;
		left: 0;
		right: 0;
		margin-top: 0;
		border-radius: 50%;
		background-color: var(--primary-color);
		background-image: none;

		@media (max-width: 767px) {
			height: 40px;
			width: 40px;
		}

		&:before {
			position: absolute;
			font-family: 'icomoon' !important;
			speak: none;
			font-style: normal;
			font-weight: normal;
			font-variant: normal;
			text-transform: none;
			line-height: 1;
			font-size: 20px;
			left: 50%;
			top: 50%;
			transform: translate(-50%, -50%);
			color: $white;

			@media (max-width: 767px) {
				font-size: 16px;
			}
		}

		&:focus{
			outline: none;
		}
	}

	.swiper-button-prev {
		margin-right: 10px;

		@media (max-width: 767px) {
			margin-right: 5px;
		}

		&:before {
			content: "\e909";
		}
	}

	.swiper-button-next {
		&:before {
			content: "\e90a";
		}
	}
}

