/*
	Team Hero 2 Component
*/

.team-hero-2-cpn {
	padding-top: 60px;

	@media (max-width: 767px) {
		.img-ctn {
			padding-top: 20px;
		}
	}

	.description {
		position: relative;
		max-width: 530px;

		&.-opened {
			.text-ctn {
				max-height: 5000px;
			}

			.small-link {
				display: none;
			}

			.gradient {
				opacity: 0;
			}
		}

		.text-ctn {
			max-height: 210px;
			overflow: hidden;
			transition: $primaryAnimation;

			p {
				margin-top: 0;
				color: var(--color-text);
				font-family: var(--font-secondary);
				font-size: var(--font-size-small);
				line-height: 30px;
			}
		}

		.small-link {
			display: block;
			margin-top: 10px;
			font-size: var(--font-size-cta-small);
			text-transform: none;
		}

		.gradient {
			position: absolute;
			bottom: 0;
			left: 0;
			right: 0;
			height: 113px;
			width: 100%;
			background: linear-gradient(180deg, rgba(255,255,255,0) 0%, #FFFFFF 100%);
			transition: $primaryAnimation;
		}
	}

	ul {
      padding-left: 0px;
      padding-bottom: 30px;

      li {
        position: relative;
        padding: 16px 0px 16px 40px;
        // color: $listColor;
        font-family: var(--font-secondary);
        font-size: var(--font-size-small);
        line-height: var(--line-height-small);
        border-bottom: $listBorderBottom;

        &:before {
          color: var(--primary-color);
          font-family: 'icomoon';
          speak: none;
          font-style: normal;
          font-weight: normal;
          font-variant: normal;
          text-transform: none;
          line-height: 1;
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
          content: "\e90c";
          position: absolute;
          left: 0;
          top: 22px;
        }
      }
    }
}

