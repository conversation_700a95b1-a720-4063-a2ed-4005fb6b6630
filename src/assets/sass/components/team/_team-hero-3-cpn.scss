/*
	Team Hero 3 Component
*/

.team-hero-3-cpn {
	padding: 100px 0;
	background: url("/assets/images/turmel/backgrounds/bg_texture.webp");
	color: var(--color-text);

	@media (max-width: 767px) {
		padding: 64px 0;
	}

	.team-hero-content {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
    	height: 100%;
	}

	.tagline {
		margin-bottom: 32px;
		color: var(--secondary-color);
		display: block;
	}

	.page-title {
		margin: 0;
		font-weight: normal;
	}

	.img-ctn {
		height: auto;
		border-radius: 32px;

		@media (max-width: 767px) {
			height: auto;
			aspect-ratio: 16 / 9;
		}

		img {
			border-radius: 32px;
			width: 100%;
			height: 100%;
			object-fit: cover;
		}
	}

	p {
		font-family: var(--font-primary);
		@extend .--large;
	}

	.team-hero {
		align-items: center;

		@media (max-width: 992px) {
			flex-direction: column;

			.team-hero-content {
				width: 100%;
			}
		}
	}
	ul {
		padding-left: 0px;
		padding-bottom: 30px;

		li {
			position: relative;
			padding: 16px 0px 16px 40px;
			font-family: var(--font-secondary);
			font-size: var(--font-size-small);
			line-height: var(--line-height-small);
			border-bottom: $listBorderBottom;

			&:before {
				color: var(--primary-color);
				font-family: 'icomoon';
				speak: none;
				font-style: normal;
				font-weight: normal;
				font-variant: normal;
				text-transform: none;
				line-height: 1;
				-webkit-font-smoothing: antialiased;
				-moz-osx-font-smoothing: grayscale;
				content: "\e90c";
				position: absolute;
				left: 0;
				top: 22px;
			}
		}
	}
}

