/*
	Team Interstice 1 Component
*/

$teamInterstice1PaddingTop: 160px;
$teamInterstice1PaddingBottom: 160px;
$teamInterstice1InfoBoxBorder: 5px solid #004b3c33;

$teamInterstice1InfoBoxTitleSize: 22px;
$teamInterstice1InfoBoxTitleColor: #000000;
$teamInterstice1InfoBoxTitleLineHeight: 27px;

$teamInterstice1InfoBoxDescriptionSize: 14px;
$teamInterstice1InfoBoxDescriptionColor: var(--color-text);
$teamInterstice1InfoBoxDescriptionLineHeight: 24px;

.team-interstice-1-cpn {
	position: inherit;
	padding-top: $teamInterstice1PaddingTop;

	@media (max-width: 767px) {
		padding-top: 60px;
	}

	.team-info-box {
		border-radius: 40px;
		text-align: center;
		background-color: $background;
		border: none;
		@include clearfix;
		margin-bottom: 170px;

		.team-info-box-content {
			margin: 0 auto;
			float: none;
		}

		.-h2 {
			margin-top: 0;
			margin-bottom: 56px;
		}

		.description {
			font-family: var(--font-primary);
			margin-top: 0;
			margin-bottom: 40px;
			text-align: center;
			@extend .--normal;
		}
	}
}