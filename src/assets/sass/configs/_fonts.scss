/**
  * This file contains external font imports, or local custom font declarations
  */


// Google font imports example
@import url('https://fonts.googleapis.com/css2?family=Manrope:wght@200;300;400;500;600;700;800;900&display=swap');
@import url("https://use.typekit.net/ebx2cgv.css");

// Adobe import example
// @import url("https://use.typekit.net/pmz8cax.css");

// Custom font imports example
// @font-face {
//   font-family: 'PP Neue Montreal';
//   src: url('../fonts/PPNeueMontreal/PPNeueMontreal-Bold.woff2') format('woff2'),
//       url('../fonts/PPNeueMontreal/PPNeueMontreal-Bold.woff') format('woff');
//   font-weight: bold;
//   font-style: normal;
//   font-display: swap;
// }

// @font-face {
//   font-family: 'PP Neue Montreal';
//   src: url('../fonts/PPNeueMontreal/PPNeueMontreal-LightItalic.woff2') format('woff2'),
//       url('../fonts/PPNeueMontreal/PPNeueMontreal-LightItalic.woff') format('woff');
//   font-weight: 300;
//   font-style: italic;
//   font-display: swap;
// }



// Icomoon specific

// @font-face {
//   font-family: 'icomoon';
//   src:  url('../fonts/icomoon.eot?ijfbdm');
//   src:  url('../fonts/icomoon.eot?ijfbdm#iefix') format('embedded-opentype'),
//     url('../fonts/icomoon.ttf?ijfbdm') format('truetype'),
//     url('../fonts/icomoon.woff?ijfbdm') format('woff'),
//     url('../fonts/icomoon.svg?ijfbdm#icomoon') format('svg');
//   font-weight: normal;
//   font-style: normal;
//   font-display: block;
// }

// icons 
// @font-face {
//   font-family: 'icomoon';
//   src:  url('../fonts/icomoon.eot?ijfbdm');
//   src:  url('../fonts/icomoon.eot?ijfbdm#iefix') format('embedded-opentype'),
//     url('../fonts/icomoon.ttf?ijfbdm') format('truetype'),
//     url('../fonts/icomoon.woff?ijfbdm') format('woff'),
//     url('../fonts/icomoon.svg?ijfbdm#icomoon') format('svg');
//   font-weight: normal;
//   font-style: normal;
//   font-display: block;
// }
