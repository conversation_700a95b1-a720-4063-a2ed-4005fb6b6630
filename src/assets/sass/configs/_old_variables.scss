// Only here for the sole purpose of keeping the old variables to let css compile without errors


/*
	Here you can change the main font which is used for Titles and Emphase
*/
// $primaryFont: 'Lato', Arial, sans-serif;
// $primaryBold: 700;

/*
	Here you can change the main font which is used for all the texts
*/
// $secondaryFont: 'Open Sans', Arial, sans-serif;
/*
	Here you can change the secondary font for Landing Pages
*/
// $thirdFont: 'Roboto', Arial, sans-serif;

/*
	Here you can change the colors of the site
*/
// $primaryColor: #008060;
// $primaryColorDarker: darken($primaryColor, 10);
// $complementaryEmphase: #D61616;
// $secondaryMainColor: #666666;
// $secondaryComplementaryColor: #999999;

/*
	Other colors for efficiency
*/
// $black: #000000;
// $white: #FFFFFF;
// $grey: #888888;
// $red: #bb0303;
// $warning: #f7c111;

// var(--color-bg): #fbf7ee;

/*
	Animation / Transitions
*/
$primaryAnimation: all 0.45s ease;
$primaryTextAnimation: color 0.35s ease;
$primaryOpacityAnimation: opacity 0.45s ease;
$propertyAnimation: all 0.45s cubic-bezier(.72,.1,.14,1);

/*
	Titles
*/
// $h1TitleFont: $primaryFont;
// $h1TitleSize: 38px;
// $h1TitleLineHeight: 49px;
// $h1TitleWeight: normal;
// $h1TitleTransform: none;

// // $h2TitleFont: $primaryFont;
// $h2TitleSize: 30px;
// $h2TitleLineHeight: 36px;
// $h2TitleWeight: normal;
// $h2TitleTransform: none;

// // $h3TitleFont: $primaryFont;
// $h3TitleSize: 22px;
// $h3TitleLineHeight: 27px;
// $h3TitleWeight: normal;
// $h3TitleTransform: none;

// // $h4TitleFont: $primaryFont;
// $h4TitleSize: 18px;
// $h4TitleLineHeight: 28px;
// $h4TitleWeight: 600;
// $h4TitleTransform: none;

// $emphaseTitleSize: 20px;
// $emphaseLineHeight: 24px;
// $emphaseBorderColor: var(--primary-color);

/*
	Textes
*/
// $primaryTextSize: 18px;
// $primaryTextLineHeight: 32px;

// var(--font-size-small): 16px;
// $subTextLineHeight: 30px;

// $detailTextSize: 14px;
// $detailTextLineHeight: 24px;

/*
	List
*/
// $listHeight: 56px;
// $listSize: var(--font-size-small);
// $listLineHeight: 28px;
// $listColor: #444444;
$listBorderBottom: 1px solid #D8D8D8;
$listPaddingLeft: 40px;

/* Open Visit Table */
$OpenVisitTitleSize: var(--font-size-cta-small);
$OpenVisitTitleColor: $black;
$OpenVisitDesciptionSize: var(--font-size-cta-small);
$OpenVisitDesciptionColor: $black;

/*
	Filters
*/
$filterTabSize: 15px;
$filterTabLineHeight: normal;
// $filterTabFont: $primaryFont;
$filterTabWeight: 700;
$filterTabTextTransform: none;
$filterTabActiveColor: var(--primary-color);
$filterTabActiveBorderSize: 2px;
$filterTabActiveBorderColor: var(--primary-color);
$filterTabColor: var(--color-text);

/*
	Forms & Input
*/
$inputHeight: 50px;
$inputHeightLarge: 60px;
$inputColor: var(--color-text);
$inputFont: var(--font-secondary);
$inputSize: 15px;
$inputLineHeight: 40px;
$inputBorder: 1px solid #DDDDDD;
$inputPadding: 0 15px;
$inputArrowColor: var(--color-text);
$inputRadius: 5px;

$placeHolderColor: var(--color-text);
$placeHolderOpacity: 0.6;

$formLabelSize: 14px;
$formLabelLineHeight: 19px;
$formLabelFont: var(--font-secondary);
$formLabelColor: var(--color-text);

/*
	NOT CUSTOMIZABLE.
	DON'T CHANGE THOSE.
	EXPECIALLY YOU.
	YES I'M TALKING ABOUT YOU.
	..but I made this.
	I SAID NO.
*/

/*
	Grid system
*/
// $columns: 12;
// $gutter: 20px;
// $outsideGutter: 40px;
// $columnsWidth: 80px;

// :root {
// 	--grid-margin: #{$outsideGutter};
// 	--grid-columns: #{$columns};
// 	--unit: #{$gutter};
// }