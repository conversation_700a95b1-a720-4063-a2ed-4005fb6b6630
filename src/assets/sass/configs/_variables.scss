/* Set Variable to :root DOM */
:root {
    // Set main colors
    --primary-color:        #{$primary-color};
    --primary-color-darker: #{$primary-color-darker};
    --secondary-color:      #{$secondary-color};
    --complementary-color:  #{$complementary-color};
    --dark-color:           #{$dark};

    // Typo colors
    --color-text:           #{$paragraph};
    --color-title:          var(--dark-color);
    --color-bg:             #{$background};
    --color-bg-light:       #{$background-light};

    // Font family
    --font-title :      "the-seasons", sans-serif;
    --font-primary :    "Manrope", sans-serif;
    --font-secondary:   var(--font-title);


    // // // // //
    // HEADINGS // 
    // // // // //

    //--font-size-h1-large:   #{responsive-size(80px, 100px, $desktop-lg)};
    //--line-height-h1-large: #{responsive-size(70px, 90px, $desktop-lg)};
    --font-size-h1-display:         #{responsive-size(42px, 110px, $desktop-lg)};
    --line-height-h1-display:       #{responsive-size(42px, 90px, $desktop-lg)};
    --font-size-h1:         #{responsive-size(42px, 70px, $desktop-lg)};
    --line-height-h1:       #{responsive-size(50px, 72px, $desktop-lg)};
    --font-size-h1-small:   #{responsive-size(38px, 57px, $desktop-lg)};
    --line-height-h1-small: #{responsive-size(55px, 55px, $desktop-lg)};

    --font-size-h2-large:   #{responsive-size(38px, 56px, $desktop-lg)};
    --line-height-h2-large: #{responsive-size(40px, 60px, $desktop-lg)};
    --font-size-h2:         #{responsive-size(36px, 53px, $desktop-lg)};
    --line-height-h2:       #{responsive-size(40px, 60px, $desktop-lg)};
    --font-size-h2-small:   #{responsive-size(32px, 36px, $desktop-lg)};
    --line-height-h2-small: #{responsive-size(40px, 50px, $desktop-lg)};

    --font-size-h3:         #{responsive-size(32px, 40px, $desktop-lg)};
    --line-height-h3:       #{responsive-size(32px, 42px, $desktop-lg)};
    //--font-size-h3-small:   #{responsive-size(24px, 28px, $desktop-lg)}; 
    //--line-height-h3-small: #{responsive-size(34px, 38px, $desktop-lg)};

    --font-size-h4:         #{responsive-size(26px, 30px, $desktop-lg)};
    --line-height-h4:       #{responsive-size(36px, 36px, $desktop-lg)};

    --font-size-h5:         #{responsive-size(20px, 20px, $desktop-lg)};
    --line-height-h5:       #{responsive-size(20px, 20px, $desktop-lg)};

    --font-size-h6:         #{responsive-size(20px, 20px, $desktop-lg)};
    --line-height-h6:       #{responsive-size(20px, 20px, $desktop-lg)};



    // // // // // //
    //  PARAGRAPHS // 
    // // // // // //

    --font-size-large:          #{responsive-size(18px, 22px, $desktop-lg)};
    --line-height-large:        #{responsive-size(30px, 32px, $desktop-lg)};

    --font-size-medium:         #{responsive-size(16px, 17px, $desktop-lg)};
    --line-height-medium:       #{responsive-size(26px, 28px, $desktop-lg)};
    
    --font-size-small:          #{responsive-size(14px, 16px, $desktop-lg)};
    --line-height-small:        #{responsive-size(20px, 24px, $desktop-lg)};
    
    --font-size-xsmall:          #{responsive-size(11px, 13px, $desktop-lg)};
    --line-height-xsmall:        #{responsive-size(16px, 0px, $desktop-lg)};
    
    --font--size-description:   #{responsive-size(13px, 13px, $desktop-lg)};
    --line-height-description:  #{responsive-size(16px, 16px, $desktop-lg)};

    --font-size-testimony:      #{responsive-size(22px, 26px, $desktop-lg)};
    --line-height-testimony:    #{responsive-size(28px, 36px, $desktop-lg)};

    --font-size-blog:           #{responsive-size(22px, 24px, $desktop-lg)};
    --line-height-blog:         #{responsive-size(26px, 28px, $desktop-lg)};



    // // // // //
    // SPECIALS // 
    // // // // //

    // Prices
    --font-size-price-big:      #{responsive-size(20px, 22px, $desktop-lg)};
    --line-height-price-big:    #{responsive-size(24px, 26px, $desktop-lg)};
    --font-size-price-small:    #{responsive-size(14px, 18px, $desktop-lg)};
    --line-height-price-small:  #{responsive-size(16px, 18px, $desktop-lg)};

    // CTA
    --font-size-cta-large:      #{responsive-size(16px, 18px, $desktop-lg)};
    --line-height-cta-large:    #{responsive-size(20px, 22px, $desktop-lg)};
    --font-size-cta-medium:      #{responsive-size(14px, 18px, $desktop-lg)}; 
    --line-height-cta-medium:    #{responsive-size(18px, 18px, $desktop-lg)};
    --font-size-cta-small:      #{responsive-size(15px, 15px, $desktop-lg)};
    --line-height-cta-small:    #{responsive-size(18px, 18px, $desktop-lg)};

    // Surtitles
    --font-size-eyebrow-large:      #{responsive-size(15px, 18px, $desktop-lg)};
    --line-height-eyebrow-large:    #{responsive-size(20px, 22px, $desktop-lg)};
    --font-size-eyebrow:            #{responsive-size(15px, 15px, $desktop-lg)};
    --line-height-eyebrow:          #{responsive-size(20px, 20px, $desktop-lg)};

    // Thumbnails
    --font-size-thumbnail-title:        #{responsive-size(20px, 24px, $desktop-lg)};
    --line-height-thumbnail-title:      #{responsive-size(28px, 32px, $desktop-lg)};
    --font-size-thumbnail-meta:         #{responsive-size(13px, 13px, $desktop-lg)};
    --line-height-thumbnail-meta:       #{responsive-size(16px, 16px, $desktop-lg)};
    --font-size-thumbnail-description:  #{responsive-size(12px, 12px, $desktop-lg)};
    --line-height-thumbnail-description:#{responsive-size(16px, 16px, $desktop-lg)};



    // // // // 
    // GRID  // 
    // // // //

    // Spacing (todo : validé ceux des maquettes)
    --spacing-small:    #{rem(20px)};
    --spacing-normal:   #{rem(50px)};
    --spacing-medium:   #{rem(80px)};
    --spacing-large:    #{rem(120px)};
    


    // Metrics
    --unit: #{$column-gap};
    --container-margin: #{rem(150px)};

    // Grid
    --grid-columns: #{$base-column-nb};
    --grid-gutter:  var(--unit); // $column-gap in config.scss
    --grid-margin:  var(--container-margin);

    // Set a max-width for the container (useful for large screens)
    @media screen and (min-width: $desktop-xxlg) {
        --container-margin: 10vw;
    }

    // Small desktop
    @media (max-width: $desktop) {
        --container-margin: #{rem(60px)};
    }

    // Tablet and mobile
    @media screen and (max-width: $tablet) {
        // Metric
        --unit: 20px;
        --container-margin: 20px;

        // Grid
        --grid-columns: 4;

        // Spacing
        --spacing-normal:   #{rem(30px)};
        --spacing-medium:   #{rem(60px)};
        --spacing-large:    #{rem(100px)};
    }
}