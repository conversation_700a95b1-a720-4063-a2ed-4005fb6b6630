.buy-sheet-page {
	padding-top: 135px;
	padding-bottom: 135px;
	opacity: 0;
	transition: 0.5s all ease;
	background-color: $background;

	&.show { opacity: 1; }

	.page-list-ctn {
		padding-left: 0;

		& > h1, & > h2, & > h3 {
			margin-top: 0;	
		}

		.page-description {
			p, li, div {
				color: var(--color-text);
				@extend .--normal;
			}

			h3 {
				margin: 0;
				margin-bottom: 30px;
				font-family: var(--font-secondary);

				&:not(:first-child) {
					margin-top: 30px;
				}
			}
		}
	}

	.team-info-wrap {
		padding-right: grid-space(math.div(0.5,12), 1);
		padding-bottom: 30px;

		.page-title {
			font-weight: 400;
			margin: 0;
    		font-size: 48px;

			@media (max-width: 767px) {
				font-size: 36px;
			}
		}

		.page-description {
			margin: 40px 0;
		}
	}

	@media (max-width: 992px) {
		.team-info-wrap {
			padding-left: 0px;	
		}
	}

	@media (max-width: 767px) {
		padding-top: 50px;

		.page-list-ctn {
			// h1.page-title {
			// 	font-size: 24px;
			// 	line-height: 24px;
			// }
		}
	}
}