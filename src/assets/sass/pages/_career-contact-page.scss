.career-contact-cpn {
	padding-top: 60px;
	padding-bottom: 80px;
	background-color: #fbf7ee;

  .form-container {
	border-radius: 24px;
	overflow: hidden;

		&.send {
			display: flex;
			justify-content: center;
			align-items: center;

			.contact-form { opacity: 0; }
			.contact-form-ctn { opacity: 0; }
		}

		.loading-inner { opacity: 0.5; }

		.form-loader{
			position: absolute;
			left: 50%;
			top: 50%;
			transform: translate(-50%,-50%);
		}
  }

	.contact-form-ctn {
		padding: 80px 100px;
		background: white;
	}

	.form-head {
		position: relative;
		padding-bottom: 40px;
	}

	// .-page-title {
	// 	margin: 0;
	// }

	.-page-description {
		margin-top: 20px;
		margin-bottom: 0;
		color: var(--color-text);
		font-family: var(--font-secondary);
		// font-size: $primaryTextSize;
		// line-height: $primaryTextLineHeight;
	}
  
	.-page-required{
		margin: 25px 0 0;
		color: var(--color-text);
		font-family: var(--font-secondary);
		font-size: 12px;
    &--right { text-align: right; }
	}

//   .emphase-title { margin: 50px 0; }

  .button-ctn {
    display: flex;
    .main-button:first-child { margin-left: auto; }
  }

  .form-response {
    position: absolute;
    text-align: center;
    opacity: 0;
    padding: 0 15px;

    .button-ctn .main-button { margin: auto !important; }

    &.show {
      opacity: 1;
      transition: all 0.5s ease-in-out;
    }
  }

  @media (max-width: 767px) { 
    .contact-form-ctn {
      padding: 60px 15px;
    }
  }
}
