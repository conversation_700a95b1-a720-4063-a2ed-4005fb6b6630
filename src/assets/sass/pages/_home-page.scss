.home-page {
    
}

$homeCustomsPadding: 60px;

.home-customs-cpn {
    
    .container {
        position: relative;
        padding-top: $homeCustomsPadding;
        padding-bottom: $homeCustomsPadding;
    }

    .grid {

        &.-reverse {
            .img-ctn {
                order: 1;

                @media (max-width: 768px) {
                    order: unset;
                }

                img {
                    border-radius: 24px 0px 0px 24px;
                    right: 0;
                    left: unset;

                    @media (max-width: 768px) {
                        border-radius: 24px;
                    }
                }
            }

            .text-ctn {
                padding-right: grid-space(math.div(1,12), 0);
                padding-left: 0;

                @media (max-width: 768px) {
                    padding-right: 0;
                }
            }
        }
    }

    img {
        border-radius: 0px 24px 24px 0px;
        position: absolute;
        left: 0;
        top: $homeCustomsPadding;
        height: calc(100% - $homeCustomsPadding * 2);
        width: 50%;
        object-fit: cover;

        @media (max-width: 768px) {
            position: unset;
            height: auto;
            width: 100%;
            border-radius: 24px;
        }
    }

    .text-ctn {
		padding-left: grid-space(math.div(1,12), 0);
        padding-top: calc($homeCustomsPadding / 2);
        padding-bottom: calc($homeCustomsPadding / 2);

        @media (max-width: 768px) {
            padding-left: 0;
        }
    }
    
    .subtitle {
        color: var(--secondary-color);
        @extend .-eyebrow;
    }

    .title {
        margin-top: 32px;
        margin-bottom: 56px;
    }

    .text {
        margin-top: 0;
        margin-bottom: 40px;
    }
}