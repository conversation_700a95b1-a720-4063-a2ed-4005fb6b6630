/*
	Homestaging Page
*/

$homestagingPageInfoBoxBorder: 5px solid #CCCCCC;

$homestagingPageInfoBoxTitleSize: 22px;
$homestagingPageInfoBoxTitleColor: #000000;
$homestagingPageInfoBoxTitleLineHeight: 27px;

$homestagingPageInfoBoxDescriptionSize: 14px;
$homestagingPageInfoBoxDescriptionColor: var(--color-text);
$homestagingPageInfoBoxDescriptionLineHeight: 24px;

.homestaging-sheet-page {

	.page-list-ctn {
		padding-left: 0;
	}

	.team-info-wrap {
		padding-right: 0;

		@media (max-width: 992px) {
			padding-left: 0px;
			padding-bottom: 30px;
		}
	}

	.page-description {
		color: $homestagingPageInfoBoxDescriptionColor;
		font-family: var(--font-secondary);
		font-size: var(--font-size-small);
		line-height: var(--line-height-small);
	}

	.team-info-box {
		padding: 40px 20px 50px;
		border: $homestagingPageInfoBoxBorder;
		text-align: center;
		@include clearfix;

		.team-info-box-content {
			margin: 0 auto;
			float: none;
		}

		.description {
			margin-top: 0;
			margin-bottom: 30px;
			color: $homestagingPageInfoBoxDescriptionColor;
			font-family: var(--font-secondary);
			font-size: $homestagingPageInfoBoxDescriptionSize;
			line-height: $homestagingPageInfoBoxDescriptionLineHeight;
			text-align: center;
		}

		.main-button {
			min-width: 100%;

			@media (max-width: 992px) {
				min-width: inherit;
			}
		}
	}
}


.homestaging-card-ctn {
	padding: 80px 0px;
	background-color: #F4F4F4;

	@media (max-width: 767px) {
		padding: 60px 0px;
	}

	h2 {
		margin-bottom: 40px;
	}

	.homestaging {
		padding-bottom: 40px;

		@media (max-width: 767px) {
			justify-content: center;
		}

		.homestaging-ctn .-flex-between {
			gap: 20px;
			margin-bottom: 40px;
		}
	}

	.button-ctn {
		text-align: center;

		h3 {
			margin-bottom: 30px;
		}

    	.main-button { margin: auto; }
	}
}