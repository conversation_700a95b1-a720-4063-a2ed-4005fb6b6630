/*
	Property Groups Page
*/

$propertyGroupsPagePaddingTop: 120px;
$propertyGroupsPageBG: $background;

$propertyGroupsPageDescription: $grey;

$propertyGroupsPageCardPaddingTop: 120px;
$propertyGroupsPageCardPaddingBottom: 120px;

.propertygroups-page {
	padding: $propertyGroupsPagePaddingTop 0;
	background-color: var(--color-bg);

	.property-group-head {
		text-align: center;

		.page-title {
			margin-top: 0;
			margin-bottom: 56px;
		}

		.page-description {
			margin: 0 auto;
			max-width: 800px;
			color: $propertyGroupsPageDescription;
			font-size: 24px;
			font-style: normal;
			font-weight: 400;
			line-height: normal;
			text-align: center;
		}

		.main-button {
			margin: auto;
		}
	}

	.property-group-card-ctn {
		padding-top: $propertyGroupsPageCardPaddingTop;
		padding-bottom: $propertyGroupsPageCardPaddingBottom;
	}

	.property-group-cta {
		.main-button {
			margin: 0 auto;
		}
	}
}

.propertygroup-description {
	padding-bottom: 30px;
}

.property-downloads-cpn {
	padding-bottom: 40px;
}