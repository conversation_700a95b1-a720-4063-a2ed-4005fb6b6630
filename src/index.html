<!doctype html>
<html lang="fr">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
	<title></title>

	<meta name="description" content="">
    <meta property="og:site_name" content="" />
	<meta property="og:title" content="" />
	<meta property="og:description" content="" />
	<meta property="og:image" content="" />
	<meta property="og:image:width" content="" />
	<meta property="og:image:height" content="" />
	<meta property="og:url" content="" />
	<meta property="og:type" content="website" />
  	<link rel="canonical" href="" />
	<base href="/">
	<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" />
	<link rel="icon" type="image/x-icon" href="favicon.ico">

	<!-- THIS SCRIPT MUST BE PLACED BEFORE THE COOKIEBOT SCRIPT
		 Set 'data-cookieconsent="ignore"' on main.js to prevent Cookiebot
		 from blocking it when the user doesn't accept cookies -->
	<script data-cookieconsent="ignore">
		var observer = new MutationObserver(function(mutations) {
			mutations.forEach(function(mutation) {
				Array.from(mutation.addedNodes).forEach(function(node) {
					if (node.tagName === 'SCRIPT' && node.getAttribute('src') && node.getAttribute('src').startsWith('main')) {
						node.setAttribute('data-cookieconsent', 'ignore');
					}
				});
			});
		});
		observer.observe(document, { childList: true, subtree: true });
	</script>
	<!-- <script id="Cookiebot" src="https://consent.cookiebot.com/uc.js" data-cbid="9b579884-3106-4405-9f75-1ad9261f6b23" data-blockingmode="auto" type="text/javascript"></script> -->
	<script data-cookieconsent="ignore">
		if (global === undefined) var global = window;
	</script>

	<!-- ############################################# -->
	<!-- ####### A CHANGER AVANT MISE EN LIGNE ####### -->
	<!-- ############################################# -->

	<!-- Google tag (gtag.js) -->
	<script async src="https://www.googletagmanager.com/gtag/js?id=G-HT849YZL5D"></script>
	<script>
		window.dataLayer = window.dataLayer || [];
		function gtag(){dataLayer.push(arguments);}
		gtag('js', new Date());

		gtag('config', 'G-HT849YZL5D');
	</script>
	<style>
		.showinie { display: none; }
	</style>
</head>

<body>
	<div id="banner-ie">
		<div>
			<b>Votre navigateur Web (Internet Explorer 11 ou 10) n'est pas à jour.</b> </br>
			Mettez à jour votre navigateur pour plus de sécurité et de rapidité et la meilleure expérience sur ce site.
			<a href="https://www.microsoft.com/fr-ca/windows/microsoft-edge">Mettre à jour le navigateur</a>
			<a id="chrome" href="https://www.google.fr/chrome/">Chrome</a>
			<a id="firefox" href="https://www.mozilla.org/fr/firefox/new/">Firefox</a>
		</div>
		<div>
			<b>Your web browser (Internet Explorer 11 or 10) is not up to date.</b> </br>
			Update your browser for more security and speed and the best experience on this site.
			<a href="https://www.microsoft.com/en-ca/windows/microsoft-edge">Update browser</a>
			<a id="chrome" href="https://www.google.com/chrome/">Chrome</a>
			<a id="firefox" href="https://www.mozilla.org/en-US/firefox/new/">Firefox</a>
		</div>
	</div>

	<e-closion></e-closion>

	<script>
		if ('serviceWorker' in navigator) {
			navigator.serviceWorker.register('/service-worker.js').then(function(registration) {
				console.log('Service Worker registered : '+registration.scope);
		  	}).catch(function(err) {
		    console.log('Service Worker registration failed: ', err);
		  });
		}
	</script>

	<script src="https://addevent.com/libs/atc/1.6.1/atc.min.js"></script>

	<script>
		var isIE11 = !!window.MSInputMethodContext && !!document.documentMode;
		if(!isIE11){
			var element = document.getElementById("banner-ie");
			element.classList.add("showinie");

			var elementremove = document.getElementById("notIEHide");
			if(elementremove)
				elementremove.remove();
		}
	</script>

</body>
</html>
